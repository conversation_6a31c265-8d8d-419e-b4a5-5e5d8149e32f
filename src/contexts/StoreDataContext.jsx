import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

const StoreDataContext = createContext();

export const useStoreData = () => {
  const context = useContext(StoreDataContext);
  if (!context) {
    throw new Error('useStoreData must be used within a StoreDataProvider');
  }
  return context;
};

export const StoreDataProvider = ({ children }) => {
  // Data states
  const [trendingApps, setTrendingApps] = useState([]);
  const [popularApps, setPopularApps] = useState([]);
  const [newApps, setNewApps] = useState([]);
  const [recommendedApps, setRecommendedApps] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [installedUtilities, setInstalledUtilities] = useState([]);

  // Loading states
  const [loadingTrending, setLoadingTrending] = useState(true);
  const [loadingPopular, setLoadingPopular] = useState(true);
  const [loadingNew, setLoadingNew] = useState(true);
  const [loadingRecommended, setLoadingRecommended] = useState(true);
  const [loadingSearch, setLoadingSearch] = useState(false);

  // Cache flags
  const [dataLoaded, setDataLoaded] = useState(false);

  // API Base URL
  const API_BASE_URL = 'http://localhost:8000';

  // Fallback data when API is not available
  const fallbackApps = [
    {
      app_id: 'json-to-csv-converter',
      app_name: 'JSON to CSV Converter',
      short_description: 'Convert JSON data to CSV format',
      rating: '4.9',
      installs: '12.3K',
      category: 'Data Conversion',
      installer_url: '/Apps/json-to-csv/json-to-csv-converter-v2.0.0.zip'
    }
  ];

  // API Functions
  const fetchTrendingApps = async () => {
    try {
      setLoadingTrending(true);
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/trending?limit=4`);
      const data = await response.json();
      setTrendingApps(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Failed to fetch trending apps:', error);
      // Use fallback data when API is not available
      setTrendingApps(fallbackApps);
    } finally {
      setLoadingTrending(false);
    }
  };

  const fetchPopularApps = async () => {
    try {
      setLoadingPopular(true);
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/popular?limit=4`);
      const data = await response.json();
      setPopularApps(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Failed to fetch popular apps:', error);
      // Use fallback data when API is not available
      setPopularApps(fallbackApps);
    } finally {
      setLoadingPopular(false);
    }
  };

  const fetchNewApps = async () => {
    try {
      setLoadingNew(true);
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/new?limit=4`);
      const data = await response.json();
      setNewApps(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Failed to fetch new apps:', error);
      // Use fallback data when API is not available
      setNewApps(fallbackApps);
    } finally {
      setLoadingNew(false);
    }
  };

  const fetchRecommendedApps = async () => {
    try {
      setLoadingRecommended(true);
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/recommended?limit=4`);
      const data = await response.json();
      setRecommendedApps(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Failed to fetch recommended apps:', error);
      // Use fallback data when API is not available
      setRecommendedApps(fallbackApps);
    } finally {
      setLoadingRecommended(false);
    }
  };

  const searchApps = useCallback(async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      setLoadingSearch(false);
      return;
    }

    try {
      setLoadingSearch(true);
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/search?q=${encodeURIComponent(query)}&limit=10`);
      const data = await response.json();
      const results = Array.isArray(data) ? data : [];
      setSearchResults(results);

      // Stop loading regardless of results to prevent infinite calls
      setLoadingSearch(false);
    } catch (error) {
      console.error('Failed to search apps:', error);
      setSearchResults([]);
      setLoadingSearch(false);
    }
  }, []);

  const loadInstalledUtilities = async () => {
    try {
      if (window.electronAPI) {
        const utilities = await window.electronAPI.getInstalledUtilities();
        setInstalledUtilities(utilities);
      }
    } catch (error) {
      console.error('Failed to load installed utilities:', error);
    }
  };

  // Load all data on first mount
  useEffect(() => {
    if (!dataLoaded) {
      const loadAllData = async () => {
        await Promise.all([
          fetchTrendingApps(),
          fetchPopularApps(),
          fetchNewApps(),
          fetchRecommendedApps(),
          loadInstalledUtilities()
        ]);
        setDataLoaded(true);
      };

      loadAllData();
    }
  }, [dataLoaded]);

  const value = {
    // Data
    trendingApps,
    popularApps,
    newApps,
    recommendedApps,
    searchResults,
    installedUtilities,
    
    // Loading states
    loadingTrending,
    loadingPopular,
    loadingNew,
    loadingRecommended,
    loadingSearch,
    
    // Functions
    searchApps,
    loadInstalledUtilities,
    
    // Cache status
    dataLoaded
  };

  return (
    <StoreDataContext.Provider value={value}>
      {children}
    </StoreDataContext.Provider>
  );
};
