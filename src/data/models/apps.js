const { getDatabase } = require('../db');
const { log } = require('../../common/log');
const DateUtils = require('../../common/dateUtils');

/**
 * Apps model - handles CRUD operations for installed applications
 */
class AppsModel {
  constructor() {
    this.db = getDatabase().apps;
    this.logger = log.scope('AppsModel');
  }

  /**
   * Create a new app entry
   */
  async createApp(appData) {
    try {
      // Validate required fields
      if (!appData.app_id || !appData.name || !appData.version) {
        throw new Error('Missing required fields: app_id, name, version');
      }

      // Check if app already exists
      const existingApp = await this.getAppById(appData.app_id);
      if (existingApp) {
        throw new Error(`App with ID ${appData.app_id} already exists`);
      }

      const appEntry = {
        app_id: appData.app_id,
        name: appData.name,
        description: appData.description || '',
        version: appData.version,
        category: appData.category || 'Utilities',
        sub_category: appData.sub_category || '',
        installer_url: appData.installer_url || '',
        local_path: appData.local_path || '',
        language: appData.language || 'python',
        requirements: appData.requirements || [],
        installed_at: DateUtils.toISOString(),
        last_run_at: null,
        run_count: 0,
        source: appData.source || 'unknown',
        destination: appData.destination || 'unknown',
        format: appData.format || 'config.json'
      };

      const result = await this.db.insert(appEntry);
      this.logger.success(`Created app: ${appData.name} (${appData.app_id})`);
      return result;
    } catch (error) {
      this.logger.error('Error creating app:', error);
      throw error;
    }
  }

  /**
   * Get app by ID
   */
  async getAppById(appId) {
    try {
      const app = await this.db.findOne({ app_id: appId });
      return app;
    } catch (error) {
      this.logger.error(`Error getting app ${appId}:`, error);
      throw error;
    }
  }

  /**
   * Get all installed apps
   */
  async getAllApps() {
    try {
      const apps = await this.db.find({}).sort({ installed_at: -1 });
      return apps;
    } catch (error) {
      this.logger.error('Error getting all apps:', error);
      throw error;
    }
  }

  /**
   * Get apps by category
   */
  async getAppsByCategory(category) {
    try {
      const apps = await this.db.find({ category }).sort({ name: 1 });
      return apps;
    } catch (error) {
      this.logger.error(`Error getting apps by category ${category}:`, error);
      throw error;
    }
  }

  /**
   * Update app information
   */
  async updateApp(appId, updateData) {
    try {
      const result = await this.db.update(
        { app_id: appId },
        { $set: updateData },
        { returnUpdatedDocs: true }
      );

      if (result.numAffected === 0) {
        throw new Error(`App with ID ${appId} not found`);
      }

      this.logger.success(`Updated app: ${appId}`);
      return result.affectedDocuments ? result.affectedDocuments[0] : result;
    } catch (error) {
      this.logger.error(`Error updating app ${appId}:`, error);
      throw error;
    }
  }

  /**
   * Increment run count and update last run time
   */
  async recordAppRun(appId) {
    try {
      const result = await this.db.update(
        { app_id: appId },
        {
          $set: { last_run_at: DateUtils.toISOString() },
          $inc: { run_count: 1 }
        },
        { returnUpdatedDocs: true }
      );

      if (result.numAffected === 0) {
        throw new Error(`App with ID ${appId} not found`);
      }

      this.logger.info(`Recorded run for app: ${appId}`);
      return result.affectedDocuments ? result.affectedDocuments[0] : result;
    } catch (error) {
      this.logger.error(`Error recording run for app ${appId}:`, error);
      throw error;
    }
  }

  /**
   * Delete app by ID
   */
  async deleteApp(appId) {
    try {
      const result = await this.db.remove({ app_id: appId });
      
      if (result === 0) {
        throw new Error(`App with ID ${appId} not found`);
      }

      this.logger.success(`Deleted app: ${appId}`);
      return result;
    } catch (error) {
      this.logger.error(`Error deleting app ${appId}:`, error);
      throw error;
    }
  }

  /**
   * Check if app is installed
   */
  async isAppInstalled(appId) {
    try {
      const app = await this.getAppById(appId);
      return !!app;
    } catch (error) {
      this.logger.error(`Error checking if app ${appId} is installed:`, error);
      return false;
    }
  }

  /**
   * Get recently installed apps
   */
  async getRecentlyInstalled(limit = 10) {
    try {
      const apps = await this.db.find({})
        .sort({ installed_at: -1 })
        .limit(limit);
      return apps;
    } catch (error) {
      this.logger.error('Error getting recently installed apps:', error);
      throw error;
    }
  }

  /**
   * Get frequently used apps
   */
  async getFrequentlyUsed(limit = 10) {
    try {
      const apps = await this.db.find({ run_count: { $gt: 0 } })
        .sort({ run_count: -1 })
        .limit(limit);
      return apps;
    } catch (error) {
      this.logger.error('Error getting frequently used apps:', error);
      throw error;
    }
  }

  /**
   * Search apps by name or description
   */
  async searchApps(query) {
    try {
      const regex = new RegExp(query, 'i');
      const apps = await this.db.find({
        $or: [
          { name: regex },
          { description: regex }
        ]
      }).sort({ name: 1 });
      return apps;
    } catch (error) {
      this.logger.error(`Error searching apps with query "${query}":`, error);
      throw error;
    }
  }

  /**
   * Get app statistics
   */
  async getAppStats() {
    try {
      const totalApps = await this.db.count({});
      const appsWithRuns = await this.db.count({ run_count: { $gt: 0 } });
      const totalRuns = await this.db.find({}).then(apps => 
        apps.reduce((sum, app) => sum + (app.run_count || 0), 0)
      );

      return {
        total_apps: totalApps,
        active_apps: appsWithRuns,
        total_runs: totalRuns
      };
    } catch (error) {
      this.logger.error('Error getting app statistics:', error);
      throw error;
    }
  }

  /**
   * Clean up orphaned apps (apps without local files)
   */
  async cleanupOrphanedApps() {
    const fs = require('fs');
    
    try {
      const apps = await this.getAllApps();
      const orphanedApps = [];

      for (const app of apps) {
        if (app.local_path && !fs.existsSync(app.local_path)) {
          orphanedApps.push(app);
        }
      }

      if (orphanedApps.length > 0) {
        for (const app of orphanedApps) {
          await this.deleteApp(app.app_id);
        }
        this.logger.warn(`Cleaned up ${orphanedApps.length} orphaned apps`);
      }

      return orphanedApps.length;
    } catch (error) {
      this.logger.error('Error cleaning up orphaned apps:', error);
      throw error;
    }
  }
}

module.exports = AppsModel;
