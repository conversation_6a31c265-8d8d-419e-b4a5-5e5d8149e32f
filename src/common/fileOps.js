const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
const AdmZip = require('adm-zip');
const signale = require('signale');

/**
 * File operations helper module
 * Handles file I/O, zip operations, and downloads
 */
class FileOps {
  constructor() {
    this.logger = signale.scope('FileOps');
  }

  /**
   * Ensure directory exists, create if not
   */
  ensureDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      this.logger.success(`Created directory: ${dirPath}`);
    }
  }

  /**
   * Read file as text
   */
  readFile(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }
      return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
      this.logger.error(`Error reading file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Read JSON file safely
   */
  readJsonFile(filePath, defaultValue = {}) {
    try {
      if (!fs.existsSync(filePath)) {
        return defaultValue;
      }
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      this.logger.error(`Error reading JSON file ${filePath}:`, error);
      return defaultValue;
    }
  }

  /**
   * Write JSON file safely
   */
  writeJsonFile(filePath, data) {
    try {
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      this.logger.success(`Written JSON file: ${filePath}`);
    } catch (error) {
      this.logger.error(`Error writing JSON file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Download file from URL to destination
   */
  async downloadFile(url, destinationPath) {
    return new Promise((resolve, reject) => {
      this.logger.info(`Downloading: ${url}`);

      // Handle relative URLs (convert to localhost URL in development)
      if (url.startsWith('/') && !url.startsWith('//')) {
        const isDev = process.env.NODE_ENV === 'development' || !require('electron').app.isPackaged;
        if (isDev) {
          url = `http://localhost:3000${url}`;
        } else {
          // In production, treat as local file path relative to app resources
          const appPath = require('electron').app.getAppPath();
          const localPath = path.join(appPath, 'build', url.substring(1));
          if (fs.existsSync(localPath)) {
            fs.copyFileSync(localPath, destinationPath);
            resolve(destinationPath);
            return;
          } else {
            reject(new Error(`Local file not found: ${localPath}`));
            return;
          }
        }
      }

      // Handle absolute local file paths (for development)
      if (!url.startsWith('http')) {
        if (fs.existsSync(url)) {
          fs.copyFileSync(url, destinationPath);
          resolve(destinationPath);
          return;
        } else {
          reject(new Error(`Local file not found: ${url}`));
          return;
        }
      }

      // Handle HTTP/HTTPS URLs
      const file = fs.createWriteStream(destinationPath);
      const protocol = url.startsWith('https:') ? https : http;

      protocol.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`Failed to download: ${response.statusCode}`));
          return;
        }

        response.pipe(file);

        file.on('finish', () => {
          file.close();
          this.logger.success(`Downloaded: ${destinationPath}`);
          resolve(destinationPath);
        });

        file.on('error', (err) => {
          fs.unlink(destinationPath, () => {}); // Delete the file on error
          reject(err);
        });
      }).on('error', (err) => {
        reject(err);
      });
    });
  }

  /**
   * Extract zip file to destination
   */
  extractZip(zipPath, extractPath) {
    try {
      this.logger.info(`Extracting: ${zipPath} to ${extractPath}`);
      
      if (fs.existsSync(extractPath)) {
        fs.rmSync(extractPath, { recursive: true, force: true });
      }
      fs.mkdirSync(extractPath, { recursive: true });

      const zip = new AdmZip(zipPath);
      zip.extractAllTo(extractPath, true);
      
      this.logger.success(`Extracted: ${extractPath}`);
      return extractPath;
    } catch (error) {
      this.logger.error(`Error extracting zip:`, error);
      throw error;
    }
  }

  /**
   * Remove directory recursively
   */
  removeDirectory(dirPath) {
    try {
      if (fs.existsSync(dirPath)) {
        fs.rmSync(dirPath, { recursive: true, force: true });
        this.logger.success(`Removed directory: ${dirPath}`);
      }
    } catch (error) {
      this.logger.error(`Error removing directory ${dirPath}:`, error);
      throw error;
    }
  }

  /**
   * Copy file from source to destination
   */
  copyFile(sourcePath, destinationPath) {
    try {
      fs.copyFileSync(sourcePath, destinationPath);
      this.logger.success(`Copied: ${sourcePath} -> ${destinationPath}`);
    } catch (error) {
      this.logger.error(`Error copying file:`, error);
      throw error;
    }
  }

  /**
   * Move/rename file or directory
   */
  moveFile(sourcePath, destinationPath) {
    try {
      fs.renameSync(sourcePath, destinationPath);
      this.logger.success(`Moved: ${sourcePath} -> ${destinationPath}`);
    } catch (error) {
      this.logger.error(`Error moving file:`, error);
      throw error;
    }
  }

  /**
   * Check if file exists
   */
  fileExists(filePath) {
    return fs.existsSync(filePath);
  }

  /**
   * Find file in multiple possible locations
   */
  findFile(possiblePaths) {
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        this.logger.info(`Found file at: ${possiblePath}`);
        return possiblePath;
      }
    }
    return null;
  }

  /**
   * Clean up temporary files
   */
  cleanupTempFiles(tempPaths) {
    tempPaths.forEach(tempPath => {
      try {
        if (fs.existsSync(tempPath)) {
          if (fs.lstatSync(tempPath).isDirectory()) {
            fs.rmSync(tempPath, { recursive: true, force: true });
          } else {
            fs.unlinkSync(tempPath);
          }
          this.logger.info(`Cleaned up: ${tempPath}`);
        }
      } catch (error) {
        this.logger.warn(`Failed to cleanup ${tempPath}:`, error.message);
      }
    });
  }
}

module.exports = FileOps;
