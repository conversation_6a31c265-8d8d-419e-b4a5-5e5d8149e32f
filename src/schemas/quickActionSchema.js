const { z } = require('zod');

/**
 * Zod schemas for QuickAction validation
 * Ensures type safety for file processing and app matching
 */

// File information schema
const FileInfoSchema = z.object({
  path: z.string().min(1, 'File path is required'),
  name: z.string().min(1, 'File name is required'),
  extension: z.string().regex(/^\.[a-zA-Z0-9]+$/, 'Invalid file extension format'),
  size: z.number().min(0, 'File size must be non-negative'),
  exists: z.boolean()
});

// Compatible app schema
const CompatibleAppSchema = z.object({
  app_id: z.string().min(1, 'App ID is required'),
  name: z.string().min(1, 'App name is required'),
  description: z.string().optional(),
  version: z.string().min(1, 'Version is required'),
  category: z.string().optional(),
  supportedExtensions: z.array(z.string()).optional(),
  compatibilityScore: z.number().min(0, 'Compatibility score must be non-negative'),
  run_count: z.number().min(0, 'Run count must be non-negative').optional(),
  last_run_at: z.string().nullable().optional()
});

// QuickAction request schema
const QuickActionRequestSchema = z.object({
  filePaths: z.array(z.string().min(1, 'File path cannot be empty'))
    .min(1, 'At least one file path is required')
    .max(10, 'Maximum 10 files allowed'),
  source: z.enum(['cli', 'ui', 'api']).default('cli')
});

// QuickAction response schema
const QuickActionResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  files: z.array(FileInfoSchema).optional(),
  compatibleApps: z.array(CompatibleAppSchema).optional(),
  supportedExtensions: z.array(z.string()).optional(),
  error: z.any().optional()
});

// File type mapping schema
const FileTypeMappingSchema = z.record(
  z.string().regex(/^\.[a-zA-Z0-9]+$/, 'Invalid file extension format'),
  z.array(z.string().min(1, 'App ID cannot be empty'))
);

// App file type support registration schema
const FileTypeSupportSchema = z.object({
  appId: z.string().min(1, 'App ID is required'),
  extensions: z.union([
    z.string().min(1, 'Extension cannot be empty'),
    z.array(z.string().min(1, 'Extension cannot be empty'))
  ])
});

// QuickAction configuration schema
const QuickActionConfigSchema = z.object({
  maxFiles: z.number().min(1).max(50).default(10),
  maxFileSize: z.number().min(1024).default(100 * 1024 * 1024), // 100MB default
  allowedExtensions: z.array(z.string()).optional(),
  blockedExtensions: z.array(z.string()).default(['.exe', '.bat', '.sh', '.cmd']),
  autoSelectSingleApp: z.boolean().default(true),
  showRecentlyUsed: z.boolean().default(true)
});

// Validation functions
const validateQuickActionRequest = (data) => {
  try {
    return {
      success: true,
      data: QuickActionRequestSchema.parse(data)
    };
  } catch (error) {
    return {
      success: false,
      error: error.errors || error.message
    };
  }
};

const validateFileInfo = (data) => {
  try {
    return {
      success: true,
      data: FileInfoSchema.parse(data)
    };
  } catch (error) {
    return {
      success: false,
      error: error.errors || error.message
    };
  }
};

const validateCompatibleApp = (data) => {
  try {
    return {
      success: true,
      data: CompatibleAppSchema.parse(data)
    };
  } catch (error) {
    return {
      success: false,
      error: error.errors || error.message
    };
  }
};

const validateFileTypeSupport = (data) => {
  try {
    return {
      success: true,
      data: FileTypeSupportSchema.parse(data)
    };
  } catch (error) {
    return {
      success: false,
      error: error.errors || error.message
    };
  }
};

const validateQuickActionConfig = (data) => {
  try {
    return {
      success: true,
      data: QuickActionConfigSchema.parse(data)
    };
  } catch (error) {
    return {
      success: false,
      error: error.errors || error.message
    };
  }
};

// Helper functions
const normalizeFileExtension = (extension) => {
  if (!extension) return '';
  return extension.startsWith('.') ? extension.toLowerCase() : `.${extension.toLowerCase()}`;
};

const isValidFileExtension = (extension) => {
  const normalized = normalizeFileExtension(extension);
  return /^\.[a-zA-Z0-9]+$/.test(normalized);
};

const sanitizeFilePath = (filePath) => {
  if (typeof filePath !== 'string') return '';
  
  // Remove any potentially dangerous characters
  return filePath.replace(/[<>:"|?*]/g, '').trim();
};

module.exports = {
  // Schemas
  FileInfoSchema,
  CompatibleAppSchema,
  QuickActionRequestSchema,
  QuickActionResponseSchema,
  FileTypeMappingSchema,
  FileTypeSupportSchema,
  QuickActionConfigSchema,
  
  // Validation functions
  validateQuickActionRequest,
  validateFileInfo,
  validateCompatibleApp,
  validateFileTypeSupport,
  validateQuickActionConfig,
  
  // Helper functions
  normalizeFileExtension,
  isValidFileExtension,
  sanitizeFilePath
};
