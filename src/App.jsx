import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Sidebar from './components/Sidebar';
import HomeView from './components/HomeView';
import StoreView from './components/StoreView';
import PreferencesView from './components/PreferencesView';
import MyAppsView from './components/MyAppsView';
import UtilityRunnerView from './components/UtilityRunnerView';
import AppDetailsView from './components/AppDetailsView';
import SeeAllView from './components/SeeAllView';
import ErrorBoundary from './components/ErrorBoundary';
import { StoreDataProvider } from './contexts/StoreDataContext';
import { Bug } from 'lucide-react';
import './index.css';

const App = () => {
  const [sidebarVisible, setSidebarVisible] = useState(true);

  const handleDebugToggle = async () => {
    console.log('🐛 Debug button clicked');
    if (window.electronAPI) {
      try {
        console.log('🔧 Calling toggleDevTools...');
        await window.electronAPI.toggleDevTools();
        console.log('✅ DevTools toggle completed');
      } catch (error) {
        console.error('❌ Failed to toggle devtools:', error);
      }
    } else {
      console.warn('⚠️ electronAPI not available');
    }
  };

  return (
    <ErrorBoundary>
      <StoreDataProvider>
        <Router>
          <div className="flex h-screen bg-macos-bg font-system" data-testid="app-container">
            {/* Draggable title bar - only show in Electron */}
            {window.electronAPI && (
              <div className="drag-region fixed top-0 left-0 right-0 h-8 z-50" />
            )}

            {/* Debug Console Button - Top Right */}
            {window.electronAPI && (
              <button
                onClick={handleDebugToggle}
                className="fixed top-2 right-2 z-50 p-2 bg-gray-800/80 hover:bg-gray-700/80 text-white rounded-lg shadow-lg transition-all duration-200 hover:scale-105"
                title="Toggle Debug Console"
              >
                <Bug className="h-4 w-4" />
              </button>
            )}

            {/* Sidebar with toggle functionality */}
            <div className={`${sidebarVisible ? 'w-64' : 'w-0'} flex-shrink-0 overflow-hidden`} data-testid="sidebar">
              <Sidebar
                isVisible={sidebarVisible}
                onToggle={() => setSidebarVisible(!sidebarVisible)}
              />
            </div>

            {/* Main content area */}
            <main className="flex-1 overflow-hidden relative" data-testid="main-content">
              <Routes>
                <Route path="/" element={<HomeView />} />
                <Route path="/store" element={<StoreView />} />
                <Route path="/store/app/:appId" element={<AppDetailsView />} />
                <Route path="/store/see-all/:section" element={<SeeAllView />} />
                <Route path="/preferences" element={<PreferencesView />} />
                <Route path="/my-apps" element={<MyAppsView />} />
                <Route path="/app/:utilityId" element={<UtilityRunnerView />} />
              </Routes>
            </main>
          </div>
        </Router>
      </StoreDataProvider>
    </ErrorBoundary>
  );
};

export default App;
