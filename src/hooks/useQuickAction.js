import { useState, useEffect, useCallback } from 'react';
import { useLogger } from './useLogger';

/**
 * Custom hook for handling QuickAction functionality
 * Manages file processing, app matching, and dialog state
 */
export function useQuickAction() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [quickActionFiles, setQuickActionFiles] = useState([]);
  const [compatibleApps, setCompatibleApps] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  
  const logger = useLogger('quickaction');

  // Listen for QuickAction files from main process
  useEffect(() => {
    const handleQuickActionFiles = (event, filePaths) => {
      logger.start(`Received QuickAction files: ${filePaths.length} files`);
      processQuickActionFiles(filePaths);
    };

    // Add event listener
    if (window.electronAPI?.onQuickActionFiles) {
      window.electronAPI.onQuickActionFiles(handleQuickActionFiles);
    }

    // Cleanup
    return () => {
      if (window.electronAPI?.removeQuickActionFilesListener) {
        window.electronAPI.removeQuickActionFilesListener(handleQuickActionFiles);
      }
    };
  }, []);

  /**
   * Process QuickAction files and find compatible apps
   */
  const processQuickActionFiles = useCallback(async (filePaths) => {
    if (!filePaths || filePaths.length === 0) {
      logger.warn('No file paths provided for QuickAction');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      logger.start(`Processing ${filePaths.length} files for QuickAction`);
      
      const result = await window.electronAPI.processQuickActionFiles(filePaths);
      
      if (result.success) {
        setQuickActionFiles(result.files || []);
        setCompatibleApps(result.compatibleApps || []);
        setIsDialogOpen(true);
        
        logger.success(`Found ${result.compatibleApps?.length || 0} compatible apps`);
      } else {
        setError(result.message || 'Failed to process files');
        logger.error('QuickAction processing failed:', result.message);
        
        // Still show dialog even if no compatible apps found
        setQuickActionFiles(result.files || []);
        setCompatibleApps([]);
        setIsDialogOpen(true);
      }
    } catch (err) {
      const errorMessage = err.message || 'Unknown error occurred';
      setError(errorMessage);
      logger.error('QuickAction error:', err);
      
      // Show error dialog
      setQuickActionFiles([]);
      setCompatibleApps([]);
      setIsDialogOpen(true);
    } finally {
      setIsProcessing(false);
    }
  }, [logger]);

  /**
   * Run selected app with QuickAction files
   */
  const runAppWithFiles = useCallback(async (app, files) => {
    if (!app || !files || files.length === 0) {
      logger.warn('Invalid app or files for QuickAction execution');
      return;
    }

    try {
      logger.start(`Running ${app.name} with ${files.length} files`);
      
      // Prepare file data for the app
      const fileData = await Promise.all(
        files.map(async (file) => {
          try {
            const content = await window.electronAPI.readFile(file.path);
            return {
              name: file.name,
              path: file.path,
              content: content,
              size: file.size,
              extension: file.extension
            };
          } catch (readError) {
            logger.error(`Failed to read file ${file.path}:`, readError);
            return {
              name: file.name,
              path: file.path,
              content: null,
              size: file.size,
              extension: file.extension,
              error: readError.message
            };
          }
        })
      );

      // Run the utility with the file data
      const result = await window.electronAPI.runUtility(
        app.app_id,
        fileData,
        { source: 'quickaction' }
      );

      logger.success(`Successfully executed ${app.name}`);
      
      // Show success notification or open output folder
      if (result.outputPath) {
        const shouldOpenFolder = window.confirm(
          `Conversion completed! Would you like to open the output folder?`
        );
        
        if (shouldOpenFolder) {
          await window.electronAPI.openFolder(result.outputPath);
        }
      }

      return result;
    } catch (err) {
      logger.error(`Failed to run ${app.name}:`, err);
      throw err;
    }
  }, [logger]);

  /**
   * Close QuickAction dialog
   */
  const closeDialog = useCallback(() => {
    setIsDialogOpen(false);
    setQuickActionFiles([]);
    setCompatibleApps([]);
    setError(null);
  }, []);

  /**
   * Open output folder (converted files folder)
   */
  const openOutputFolder = useCallback(async () => {
    try {
      const userDataPath = await window.electronAPI.getUserDataPath();
      const outputPath = `${userDataPath}/../converted`; // Assuming converted folder is at project root
      await window.electronAPI.openFolder(outputPath);
      logger.success('Opened output folder');
    } catch (err) {
      logger.error('Failed to open output folder:', err);
    }
  }, [logger]);

  /**
   * Get supported file extensions
   */
  const getSupportedExtensions = useCallback(async () => {
    try {
      return await window.electronAPI.getSupportedExtensions();
    } catch (err) {
      logger.error('Failed to get supported extensions:', err);
      return [];
    }
  }, [logger]);

  /**
   * Get apps that support a specific file extension
   */
  const getAppsForExtension = useCallback(async (extension) => {
    try {
      return await window.electronAPI.getAppsForExtension(extension);
    } catch (err) {
      logger.error(`Failed to get apps for extension ${extension}:`, err);
      return [];
    }
  }, [logger]);

  /**
   * Register file type support for an app
   */
  const registerFileTypeSupport = useCallback(async (appId, extensions) => {
    try {
      const result = await window.electronAPI.registerFileTypeSupport(appId, extensions);
      if (result) {
        logger.success(`Registered file type support for ${appId}: ${extensions.join(', ')}`);
      }
      return result;
    } catch (err) {
      logger.error(`Failed to register file type support for ${appId}:`, err);
      return false;
    }
  }, [logger]);

  /**
   * Manually trigger QuickAction with file paths
   */
  const triggerQuickAction = useCallback(async (filePaths) => {
    await processQuickActionFiles(filePaths);
  }, [processQuickActionFiles]);

  return {
    // State
    isDialogOpen,
    quickActionFiles,
    compatibleApps,
    isProcessing,
    error,
    
    // Actions
    runAppWithFiles,
    closeDialog,
    openOutputFolder,
    triggerQuickAction,
    
    // Utility functions
    getSupportedExtensions,
    getAppsForExtension,
    registerFileTypeSupport
  };
}
