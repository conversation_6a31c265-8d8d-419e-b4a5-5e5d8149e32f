const { app, BrowserWindow, ipcMain, screen, protocol, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const ContextBridge = require('./contextBridge');
const PythonRunner = require('./pythonRunnerSpawn');
const FileManager = require('./fileManager');
const { logger, createLogger } = require('../../electron/logger');

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

// Set app name for macOS menu bar
app.setName('FileDuck');

// Handle unhandled exceptions
const electronLogger = createLogger('electron');
process.on('uncaughtException', (error) => {
  electronLogger.fatal('Uncaught Exception:', error);
  if (!isDev) {
    app.quit();
  }
});

process.on('unhandledRejection', (reason, promise) => {
  logger.fatal('Unhandled Rejection at:', promise, 'reason:', reason);
  if (!isDev) {
    app.quit();
  }
});

// Initialize services
let contextBridge;
let pythonRunner;
let fileManager;

// Prevent web browser access - only allow Electron
app.commandLine.appendSwitch('disable-web-security');
if (!isDev) {
  // In production, prevent running in browser
  app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');
}

function createWindow() {
  // Get primary display dimensions for centering
  const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;

  const windowWidth = 1200;
  const windowHeight = 800;

  // Calculate center position
  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round((screenHeight - windowHeight) / 2);

  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    minWidth: 1000,
    minHeight: 700,
    icon: isDev
      ? path.join(__dirname, '../../public/duck-icon.png')
      : path.join(process.resourcesPath, 'duck-icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
    frame: process.platform !== 'darwin',
    show: false,
    title: 'FileDuck',
    movable: true,
    resizable: true
  });

  // Load the app
  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, '../../build/index.html')}`;

  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle debug console toggle
  ipcMain.handle('toggle-devtools', () => {
    if (mainWindow.webContents.isDevToolsOpened()) {
      mainWindow.webContents.closeDevTools();
    } else {
      mainWindow.webContents.openDevTools();
    }
  });

  // Utility management IPC handlers
  const ipcLogger = createLogger('ipc');
  ipcMain.handle('get-installed-utilities', async () => {
    try {
      return await contextBridge.getInstalledUtilities();
    } catch (error) {
      ipcLogger.error('Get installed utilities error:', error);
      return [];
    }
  });

  ipcMain.handle('install-utility', async (event, downloadUrl, apiAppData) => {
    try {
      return await contextBridge.installUtility(downloadUrl, apiAppData);
    } catch (error) {
      logger.error('Install utility error:', error);
      throw error;
    }
  });

  // New database-based utility methods
  ipcMain.handle('is-utility-installed', async (event, appId) => {
    try {
      return await contextBridge.isUtilityInstalled(appId);
    } catch (error) {
      ipcLogger.error('Check utility installed error:', error);
      return false;
    }
  });

  ipcMain.handle('get-utility-info', async (event, appId) => {
    try {
      return await contextBridge.getUtilityInfo(appId);
    } catch (error) {
      ipcLogger.error('Get utility info error:', error);
      return null;
    }
  });

  ipcMain.handle('get-utility-ui-component', async (event, appId) => {
    try {
      return await contextBridge.getUtilityUIComponent(appId);
    } catch (error) {
      ipcLogger.error('Get utility UI component error:', error);
      return null;
    }
  });

  ipcMain.handle('get-all-apps', async (event, options) => {
    try {
      return await contextBridge.getAllApps(options);
    } catch (error) {
      ipcLogger.error('Get all apps error:', error);
      return [];
    }
  });

  ipcMain.handle('search-apps', async (event, query) => {
    try {
      return await contextBridge.searchApps(query);
    } catch (error) {
      ipcLogger.error('Search apps error:', error);
      return [];
    }
  });

  ipcMain.handle('get-app-stats', async (event) => {
    try {
      return await contextBridge.getAppStats();
    } catch (error) {
      ipcLogger.error('Get app stats error:', error);
      return { total_apps: 0, active_apps: 0, total_runs: 0 };
    }
  });

  ipcMain.handle('get-analytics-summary', async (event, timeRange) => {
    try {
      return await contextBridge.getAnalyticsSummary(timeRange);
    } catch (error) {
      ipcLogger.error('Get analytics summary error:', error);
      return {};
    }
  });

  ipcMain.handle('get-recent-logs', async (event, limit) => {
    try {
      return await contextBridge.getRecentLogs(limit);
    } catch (error) {
      ipcLogger.error('Get recent logs error:', error);
      return [];
    }
  });

  ipcMain.handle('uninstall-utility', async (event, utilityId) => {
    try {
      return await contextBridge.uninstallUtility(utilityId);
    } catch (error) {
      logger.error('Uninstall utility error:', error);
      throw error;
    }
  });



  ipcMain.handle('run-utility', async (event, utilityId, fileData, config) => {
    try {
      const utilityInfo = await contextBridge.runUtility(utilityId, fileData, config);

      // If this is just getting utility info (no fileData), return the info
      if (!fileData || fileData.length === 0) {
        return utilityInfo;
      }

      // Otherwise, execute the utility based on runtime type
      const pythonLogger = createLogger('python');
      switch (utilityInfo.runtimeType) {
        case 'python-spawn':
          pythonLogger.start(`Executing Python utility (spawn-based): ${utilityId}`);
          // Install dependencies first
          if (utilityInfo.config.requirements) {
            await pythonRunner.installDependencies(utilityInfo.config.requirements);
          }
          return await pythonRunner.processFileData(utilityInfo.executablePath, fileData, config);

        case 'python':
          pythonLogger.start(`Executing Python utility (legacy): ${utilityId}`);
          return await pythonRunner.processFileData(utilityInfo.executablePath, fileData, config);

        case 'wasm':
          logger.warn(`WASM utilities no longer supported`);
          throw new Error('WASM utilities are no longer supported. Please use Python utilities.');

        case 'shell':
          logger.warn(`Shell utilities not yet implemented: ${utilityId}`);
          throw new Error('Shell utilities not yet implemented');

        default:
          throw new Error(`Unsupported runtime type: ${utilityInfo.runtimeType}`);
      }
    } catch (error) {
      ipcLogger.error('Run utility error:', error);
      throw error;
    }
  });

  // File management IPC handlers
  ipcMain.handle('show-open-dialog', async (event, options) => {
    try {
      return await fileManager.showOpenDialog(options);
    } catch (error) {
      ipcLogger.error('Show open dialog error:', error);
      throw error;
    }
  });

  ipcMain.handle('show-save-dialog', async (event, options) => {
    try {
      return await fileManager.showSaveDialog(options);
    } catch (error) {
      ipcLogger.error('Show save dialog error:', error);
      throw error;
    }
  });

  ipcMain.handle('read-file', async (event, filePath) => {
    try {
      return await fileManager.readFile(filePath);
    } catch (error) {
      ipcLogger.error('Read file error:', error);
      throw error;
    }
  });

  ipcMain.handle('write-file', async (event, filePath, data, options) => {
    try {
      return await fileManager.writeFile(filePath, data, options);
    } catch (error) {
      ipcLogger.error('Write file error:', error);
      throw error;
    }
  });

  ipcMain.handle('open-folder', async (event, folderPath) => {
    try {
      const fileLogger = createLogger('file');
      fileLogger.info(`Opening folder: ${folderPath}`);

      // Ensure the folder exists
      if (!fs.existsSync(folderPath)) {
        fileLogger.note(`Folder doesn't exist, creating: ${folderPath}`);
        fs.mkdirSync(folderPath, { recursive: true });
      }

      // Use shell.openPath which works better on macOS
      const result = await shell.openPath(folderPath);

      if (result) {
        fileLogger.error(`Failed to open folder: ${result}`);
        return { success: false, error: result };
      }

      fileLogger.success(`Successfully opened folder: ${folderPath}`);
      return { success: true };
    } catch (error) {
      ipcLogger.error('Open folder error:', error);
      return { success: false, error: error.message };
    }
  });

  // Python execution IPC handlers
  ipcMain.handle('execute-python', async (event, pythonPath, functionName, ...args) => {
    try {
      return await pythonRunner.executePythonFunction(pythonPath, functionName, ...args);
    } catch (error) {
      ipcLogger.error('Execute Python error:', error);
      throw error;
    }
  });

  ipcMain.handle('process-file-python', async (event, pythonPath, fileData, config) => {
    try {
      return await pythonRunner.processFileData(pythonPath, fileData, config);
    } catch (error) {
      ipcLogger.error('Process file Python error:', error);
      throw error;
    }
  });

  ipcMain.handle('process-files-python', async (event, pythonPath, filesData, config) => {
    try {
      const pythonLogger = createLogger('python');
      pythonLogger.start(`Processing ${filesData.length} files with Python`);
      const results = [];

      for (const fileData of filesData) {
        const result = await pythonRunner.processFileData(pythonPath, fileData.content, {
          ...config,
          filename: fileData.name
        });
        results.push({
          filename: fileData.name,
          result: result
        });
      }

      return results;
    } catch (error) {
      ipcLogger.error('Process files Python error:', error);
      throw error;
    }
  });



  ipcMain.handle('add-utility', async (event, data) => {
    try {
      return await contextBridge.addUtility(data);
    } catch (error) {
      ipcLogger.error('Add utility error:', error);
      throw error;
    }
  });

  ipcMain.handle('update-utility', async (event, id, data) => {
    try {
      return await contextBridge.updateUtility(id, data);
    } catch (error) {
      ipcLogger.error('Update utility error:', error);
      throw error;
    }
  });

  ipcMain.handle('get-user-data-path', () => {
    return app.getPath('userData');
  });

  // Logging IPC handlers - bridge React logs to Node.js console via Signale
  ipcMain.handle('log-message', (event, { level, scope, message, args }) => {
    const scopedLogger = scope ? createLogger(scope) : logger;

    switch (level) {
      case 'success':
        scopedLogger.success(message, ...(args || []));
        break;
      case 'complete':
        scopedLogger.complete(message, ...(args || []));
        break;
      case 'info':
        scopedLogger.info(message, ...(args || []));
        break;
      case 'note':
        scopedLogger.note(message, ...(args || []));
        break;
      case 'pending':
        scopedLogger.pending(message, ...(args || []));
        break;
      case 'await':
        scopedLogger.await(message, ...(args || []));
        break;
      case 'watch':
        scopedLogger.watch(message, ...(args || []));
        break;
      case 'start':
        scopedLogger.start(message, ...(args || []));
        break;
      case 'warn':
        scopedLogger.warn(message, ...(args || []));
        break;
      case 'pause':
        scopedLogger.pause(message, ...(args || []));
        break;
      case 'error':
        scopedLogger.error(message, ...(args || []));
        break;
      case 'fatal':
        scopedLogger.fatal(message, ...(args || []));
        break;
      case 'debug':
        scopedLogger.debug(message, ...(args || []));
        break;
      case 'fileduck':
        scopedLogger.fileduck(message, ...(args || []));
        break;
      case 'app':
        scopedLogger.app(message, ...(args || []));
        break;
      case 'install':
        scopedLogger.install(message, ...(args || []));
        break;
      case 'uninstall':
        scopedLogger.uninstall(message, ...(args || []));
        break;
      case 'python':
        scopedLogger.python(message, ...(args || []));
        break;
      case 'file':
        scopedLogger.file(message, ...(args || []));
        break;
      case 'api':
        scopedLogger.api(message, ...(args || []));
        break;
      case 'database':
        scopedLogger.database(message, ...(args || []));
        break;
      case 'slay':
        scopedLogger.slay(message, ...(args || []));
        break;
      case 'facts':
        scopedLogger.facts(message, ...(args || []));
        break;
      case 'vibe':
        scopedLogger.vibe(message, ...(args || []));
        break;
      case 'sus':
        scopedLogger.sus(message, ...(args || []));
        break;
      case 'rip':
        scopedLogger.rip(message, ...(args || []));
        break;
      case 'dev':
        scopedLogger.dev(message, ...(args || []));
        break;
      case 'fire':
        scopedLogger.fire(message, ...(args || []));
        break;
      case 'bussin':
        scopedLogger.bussin(message, ...(args || []));
        break;
      case 'periodt':
        scopedLogger.periodt(message, ...(args || []));
        break;
      default:
        scopedLogger.info(message, ...(args || []));
    }
  });

  return mainWindow;
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  // Install DevTools extensions in development
  if (isDev) {
    try {
      const { default: installExtension, REACT_DEVELOPER_TOOLS } = await import('electron-devtools-installer');

      // Force reinstall to avoid compatibility issues
      await installExtension(REACT_DEVELOPER_TOOLS, {
        loadExtensionOptions: {
          allowFileAccess: true,
        },
        forceDownload: false
      });
      electronLogger.success('React DevTools installed successfully');
    } catch (err) {
      // Silently skip DevTools installation if it fails
      // This prevents extension-related warnings in the console
    }
  }

  // Register custom protocol for installed utilities
  protocol.registerFileProtocol('installed-utilities', (request, callback) => {
    const url = request.url.substr(20); // Remove 'installed-utilities://' prefix
    const filePath = path.join(app.getPath('userData'), 'utilities', url);
    callback({ path: filePath });
  });

  // Initialize services with error handling
  try {
    contextBridge = new ContextBridge();
    electronLogger.success('ContextBridge initialized successfully');
  } catch (error) {
    electronLogger.fatal('Failed to initialize ContextBridge:', error);
    process.exit(1);
  }

  try {
    pythonRunner = new PythonRunner();
    electronLogger.success('PythonRunner initialized successfully');
  } catch (error) {
    electronLogger.fatal('Failed to initialize PythonRunner:', error);
    process.exit(1);
  }

  try {
    fileManager = new FileManager();
    electronLogger.success('FileManager initialized successfully');
  } catch (error) {
    electronLogger.fatal('Failed to initialize FileManager:', error);
    process.exit(1);
  }

  createWindow();
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
  });
});
