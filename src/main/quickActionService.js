const path = require('path');
const fs = require('fs');
const { createLogger } = require('../../electron/logger');

/**
 * QuickAction Service - Handles file type matching and app suggestions
 * for right-click context menu integration
 */
class QuickActionService {
  constructor(contextBridge) {
    this.contextBridge = contextBridge;
    this.logger = createLogger('quickaction');
    
    // File type mappings - extensible for future apps
    this.fileTypeMap = {
      // Document formats
      '.csv': ['csv-to-pdf', 'csv-to-json'],
      '.json': ['json-to-csv'],
      '.pdf': ['pdf-to-text'],
      '.txt': ['text-processor'],
      '.docx': ['docx-converter'],
      '.xlsx': ['excel-converter'],
      
      // Image formats
      '.png': ['image-converter', 'image-optimizer'],
      '.jpg': ['image-converter', 'image-optimizer'],
      '.jpeg': ['image-converter', 'image-optimizer'],
      '.webp': ['image-converter'],
      '.gif': ['image-converter'],
      
      // Video formats
      '.mp4': ['video-converter'],
      '.avi': ['video-converter'],
      '.mov': ['video-converter'],
      
      // Audio formats
      '.mp3': ['audio-converter'],
      '.wav': ['audio-converter'],
      '.flac': ['audio-converter']
    };
  }

  /**
   * Process files from CLI arguments and find compatible apps
   */
  async processQuickActionFiles(filePaths) {
    try {
      this.logger.start(`Processing ${filePaths.length} files for QuickAction`);
      
      if (!filePaths || filePaths.length === 0) {
        return { success: false, message: 'No files provided' };
      }

      // Get file information
      const fileInfos = filePaths.map(filePath => {
        const extension = path.extname(filePath).toLowerCase();
        const fileName = path.basename(filePath);
        const fileSize = fs.existsSync(filePath) ? fs.statSync(filePath).size : 0;
        
        return {
          path: filePath,
          name: fileName,
          extension,
          size: fileSize,
          exists: fs.existsSync(filePath)
        };
      });

      // Filter out non-existent files
      const validFiles = fileInfos.filter(file => file.exists);
      if (validFiles.length === 0) {
        return { 
          success: false, 
          message: 'No valid files found',
          files: fileInfos 
        };
      }

      // Find compatible apps for each file type
      const compatibleApps = await this.findCompatibleApps(validFiles);
      
      if (compatibleApps.length === 0) {
        return {
          success: false,
          message: 'No compatible apps found for the selected file types',
          files: validFiles,
          supportedExtensions: Object.keys(this.fileTypeMap)
        };
      }

      this.logger.success(`Found ${compatibleApps.length} compatible apps`);
      
      return {
        success: true,
        files: validFiles,
        compatibleApps,
        message: `Found ${compatibleApps.length} compatible app(s)`
      };

    } catch (error) {
      this.logger.error('Error processing QuickAction files:', error);
      return {
        success: false,
        message: error.message,
        error: error
      };
    }
  }

  /**
   * Find apps that can handle the given file types
   */
  async findCompatibleApps(fileInfos) {
    try {
      // Get all installed apps
      const installedApps = await this.contextBridge.getAllApps();
      
      // Get unique file extensions from the files
      const fileExtensions = [...new Set(fileInfos.map(file => file.extension))];
      
      // Find apps that support any of these extensions
      const compatibleAppIds = new Set();
      
      fileExtensions.forEach(extension => {
        const supportedApps = this.fileTypeMap[extension] || [];
        supportedApps.forEach(appId => compatibleAppIds.add(appId));
      });

      // Filter installed apps to only those that are compatible
      const compatibleApps = installedApps.filter(app => 
        compatibleAppIds.has(app.app_id)
      );

      // Enhance app info with compatibility details
      const enhancedApps = compatibleApps.map(app => ({
        ...app,
        supportedExtensions: fileExtensions.filter(ext => 
          this.fileTypeMap[ext]?.includes(app.app_id)
        ),
        compatibilityScore: this.calculateCompatibilityScore(app, fileExtensions)
      }));

      // Sort by compatibility score (highest first)
      enhancedApps.sort((a, b) => b.compatibilityScore - a.compatibilityScore);

      return enhancedApps;

    } catch (error) {
      this.logger.error('Error finding compatible apps:', error);
      return [];
    }
  }

  /**
   * Calculate compatibility score for an app based on file types
   */
  calculateCompatibilityScore(app, fileExtensions) {
    let score = 0;
    
    fileExtensions.forEach(extension => {
      const supportedApps = this.fileTypeMap[extension] || [];
      if (supportedApps.includes(app.app_id)) {
        score += 10; // Base score for supporting the file type
        
        // Bonus for exact name matches (e.g., csv-to-pdf for .csv files)
        if (app.app_id.includes(extension.substring(1))) {
          score += 5;
        }
        
        // Bonus for recent usage
        if (app.last_run_at) {
          const daysSinceLastRun = (Date.now() - new Date(app.last_run_at).getTime()) / (1000 * 60 * 60 * 24);
          if (daysSinceLastRun < 7) {
            score += 3;
          }
        }
        
        // Bonus for high usage count
        if (app.run_count > 5) {
          score += 2;
        }
      }
    });
    
    return score;
  }

  /**
   * Register a new file type mapping for an app
   */
  registerFileTypeSupport(appId, extensions) {
    try {
      if (!Array.isArray(extensions)) {
        extensions = [extensions];
      }

      extensions.forEach(ext => {
        const normalizedExt = ext.startsWith('.') ? ext : `.${ext}`;
        
        if (!this.fileTypeMap[normalizedExt]) {
          this.fileTypeMap[normalizedExt] = [];
        }
        
        if (!this.fileTypeMap[normalizedExt].includes(appId)) {
          this.fileTypeMap[normalizedExt].push(appId);
        }
      });

      this.logger.success(`Registered file type support for ${appId}: ${extensions.join(', ')}`);
      return true;

    } catch (error) {
      this.logger.error(`Error registering file type support for ${appId}:`, error);
      return false;
    }
  }

  /**
   * Get all supported file extensions
   */
  getSupportedExtensions() {
    return Object.keys(this.fileTypeMap);
  }

  /**
   * Get apps that support a specific file extension
   */
  getAppsForExtension(extension) {
    const normalizedExt = extension.startsWith('.') ? extension : `.${extension}`;
    return this.fileTypeMap[normalizedExt] || [];
  }

  /**
   * Update file type mappings from app configurations
   * This method can be called when apps are installed/updated
   */
  async updateFileTypeMappingsFromApps() {
    try {
      const installedApps = await this.contextBridge.getAllApps();
      
      for (const app of installedApps) {
        // Try to get app config to check for file type support
        const appInfo = await this.contextBridge.getUtilityInfo(app.app_id);
        
        if (appInfo && appInfo.config) {
          // Check if config has file type support defined
          if (appInfo.config.supportedFileTypes) {
            this.registerFileTypeSupport(app.app_id, appInfo.config.supportedFileTypes);
          }
          
          // Infer from app name/category if no explicit support defined
          this.inferFileTypeSupportFromAppName(app.app_id, appInfo.config);
        }
      }

      this.logger.success('Updated file type mappings from installed apps');

    } catch (error) {
      this.logger.error('Error updating file type mappings:', error);
    }
  }

  /**
   * Infer file type support from app name and metadata
   */
  inferFileTypeSupportFromAppName(appId, config) {
    const name = config.name?.toLowerCase() || '';
    const description = config.description?.toLowerCase() || '';
    const tags = config.tags || [];
    
    // Common patterns for file type inference
    const patterns = {
      'csv': ['.csv'],
      'json': ['.json'],
      'pdf': ['.pdf'],
      'image': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'video': ['.mp4', '.avi', '.mov'],
      'audio': ['.mp3', '.wav', '.flac'],
      'excel': ['.xlsx', '.xls'],
      'word': ['.docx', '.doc']
    };

    Object.entries(patterns).forEach(([keyword, extensions]) => {
      if (name.includes(keyword) || description.includes(keyword) || 
          tags.some(tag => tag.toLowerCase().includes(keyword))) {
        this.registerFileTypeSupport(appId, extensions);
      }
    });
  }
}

module.exports = QuickActionService;
