const { app } = require('electron');
const path = require('path');

// Import refactored modules
const FileOps = require('../common/fileOps');
const SpawnPython = require('../common/spawnPython');
const Utils = require('../common/utils');
const { log } = require('../common/log');

// Import database models
const AppsModel = require('../data/models/apps');
const LogsModel = require('../data/models/logs');
const AnalyticsModel = require('../data/models/analytics');
const {readdirSync} = require("node:fs");

class ContextBridge {
  constructor() {
    this.userDataPath = app.getPath('userData');
    this.utilitiesPath = path.join(this.userDataPath, 'utilities');

    // Initialize helper classes
    this.fileOps = new FileOps();
    this.pythonRunner = new SpawnPython();
    this.logger = log.scope('ContextBridge');

    // Initialize database models
    this.appsModel = new AppsModel();
    this.logsModel = new LogsModel();
    this.analyticsModel = new AnalyticsModel();

    this.ensureDirectories();
    this.initializeDatabase();
  }

  ensureDirectories() {
    // Ensure userData directory exists
    this.fileOps.ensureDirectory(this.userDataPath);

    // Ensure utilities directory exists
    this.fileOps.ensureDirectory(this.utilitiesPath);

    this.logger.success('Directories initialized');
  }

  /**
   * Initialize database with default settings
   */
  async initializeDatabase() {
    try {
      // Initialize default settings
      const SettingsModel = require('../data/models/settings');
      const settingsModel = new SettingsModel();
      await settingsModel.initializeDefaults();

      this.logger.success('Database initialized');
    } catch (error) {
      this.logger.error('Error initializing database:', error);
    }
  }

  /**
   * Get all installed utilities from database
   * Also cleans up orphaned entries
   */
  async getInstalledUtilities() {
    try {
      // Get all apps from database
      const apps = await this.appsModel.getAllApps();

      // Clean up orphaned entries (apps without local files)
      await this.appsModel.cleanupOrphanedApps();

      // Return fresh list after cleanup
      return await this.appsModel.getAllApps();
    } catch (error) {
      this.logger.error('Error getting installed utilities:', error);
      return [];
    }
  }

  /**
   * Downloads and installs utility from downloadUrl
   * New implementation using database instead of JSON file
   */
  async installUtility(downloadUrl, apiAppData = null) {
    const tempPaths = [];

    try {
      this.logger.start(`Installing utility from: ${downloadUrl}`);

      // 1. Download the zip file to temp location
      const tempZipPath = path.join(this.userDataPath, `temp_download_${Date.now()}.zip`);
      tempPaths.push(tempZipPath);
      await this.fileOps.downloadFile(downloadUrl, tempZipPath);

      // 2. Extract to get config.json and main.py
      const tempExtractPath = path.join(this.userDataPath, `temp_extract_${Date.now()}`);
      tempPaths.push(tempExtractPath);
      this.fileOps.extractZip(tempZipPath, tempExtractPath);

      // 3. Find and read config.json
      readdirSync(tempExtractPath).forEach(file => {
        this.logger.warn(file);
      });

      const configPath = path.join(tempExtractPath, 'config.json');
      this.logger.debug(`Config path: ${configPath}`);
      if (!configPath) {
        throw new Error('config.json not found in package');
      }

      const utilityConfig = this.fileOps.readJsonFile(configPath);
      const { name, version } = utilityConfig;
      this.logger.debug(`Config content:`, utilityConfig);
      if (!name || !version) {
        throw new Error('config.json must contain name and version');
      }

      // Use ID from config if available, otherwise generate one
      const utilityId = utilityConfig.id || Utils.generateUtilityId(name, version);
      const isInstalled = await this.appsModel.isAppInstalled(utilityId);

      if (isInstalled) {
        throw new Error(`Utility ${utilityId} is already installed`);
      }

      // 4. Create final utility directory
      const finalUtilityPath = path.join(this.utilitiesPath, utilityId);
      this.fileOps.moveFile(tempExtractPath, finalUtilityPath);

      // 5. Verify required files exist based on language
      const mainFilePath = path.join(finalUtilityPath, 'main.py');
      this.logger.debug(`Main file path: ${mainFilePath}`);
      if (!mainFilePath) {
        throw new Error(`${mainFilePath} not found in package`);
      }

      this.logger.success(`Found main file: ${mainFilePath}`);

      // 6. Install Python dependencies if specified
      if (utilityConfig.language === 'python' && utilityConfig.requirements) {
        this.logger.info(`Installing Python dependencies: ${utilityConfig.requirements.join(', ')}`);
        try {
          await this.pythonRunner.installDependencies(utilityConfig.requirements);
        } catch (error) {
          this.logger.warn('Failed to install some Python dependencies:', error.message);
          // Continue installation even if dependencies fail
        }
      }

      // 7. Store utility info in database
      const configRelativePath = 'config.json';

      const appData = {
        app_id: utilityId,
        name: utilityConfig.name,
        description: utilityConfig.description || apiAppData?.description || '',
        version: utilityConfig.version,
        category: apiAppData?.category || 'Utilities',
        sub_category: apiAppData?.sub_category || '',
        installer_url: downloadUrl,
        local_path: path.join(finalUtilityPath, configRelativePath),
        language: utilityConfig.language || 'python',
        requirements: utilityConfig.requirements || [],
        source: apiAppData?.source || 'unknown',
        destination: apiAppData?.destination || 'unknown',
        format: 'config.json'
      };

      await this.appsModel.createApp(appData);

      // 8. Log installation success and track analytics
      await this.logsModel.logAppInstall(utilityId, utilityConfig.name, {
        download_url: downloadUrl,
        file_size: this.fileOps.fileExists(tempZipPath) ? require('fs').statSync(tempZipPath).size : 0,
        language: utilityConfig.language,
        requirements_count: utilityConfig.requirements?.length || 0
      });

      await this.analyticsModel.trackAppInstall(utilityId, {
        app_name: utilityConfig.name,
        app_version: utilityConfig.version,
        language: utilityConfig.language,
        category: appData.category
      });

      // Cleanup temp files
      this.fileOps.cleanupTempFiles(tempPaths);

      this.logger.complete(`Utility ${name} v${version} installed successfully`);
      return { success: true, utilityId, appData };

    } catch (error) {
      this.logger.error('Install utility error:', error);

      // Cleanup on failure
      this.fileOps.cleanupTempFiles(tempPaths);

      // Log installation failure
      await this.logsModel.logSystemError(error, {
        operation: 'install_utility',
        download_url: downloadUrl
      });

      throw error;
    }
  }

  /**
   * Check if utility is installed by app_id
   */
  async isUtilityInstalled(appId) {
    try {
      return await this.appsModel.isAppInstalled(appId);
    } catch (error) {
      this.logger.error(`Error checking if utility ${appId} is installed:`, error);
      return false;
    }
  }

  /**
   * Get utility information by app_id including config
   */
  async getUtilityInfo(appId) {
    try {
      const utility = await this.appsModel.getAppById(appId);
      if (!utility) {
        return null;
      }

      // Load the config file if local_path exists
      if (utility.local_path) {
        try {
          utility.config = this.fileOps.readJsonFile(utility.local_path);
        } catch (configError) {
          this.logger.warn(`Could not load config for ${appId}:`, configError.message);
        }
      }

      return utility;
    } catch (error) {
      this.logger.error(`Error getting utility info for ${appId}:`, error);
      return null;
    }
  }

  /**
   * Get utility UI component (main.jsx) by app_id
   */
  async getUtilityUIComponent(appId) {
    try {
      const utility = await this.appsModel.getAppById(appId);
      if (!utility) {
        this.logger.warn(`Utility ${appId} not found`);
        return null;
      }

      const utilityDir = path.dirname(utility.local_path);

      // Look for main.jsx in possible locations
      const mainJsxPath = path.join(utilityDir, 'main.jsx');
      if (!mainJsxPath) {
        this.logger.warn(`No main.jsx found for utility ${appId}`);
        return null;
      }

      // Read and return the main.jsx content
      const uiComponentCode = this.fileOps.readFile(mainJsxPath);
      this.logger.success(`Loaded UI component for ${appId} from ${mainJsxPath}`);
      return uiComponentCode;
    } catch (error) {
      this.logger.error(`Error getting UI component for ${appId}:`, error);
      return null;
    }
  }

  /**
   * Finds the utility by app_id in the database,
   * detects runtime type (Python/Shell), and returns appropriate execution info
   */
  async runUtility(utilityId, fileData, config) {
    try {
      this.logger.start(`Running utility: ${utilityId}`);

      // Get utility from database
      const utility = await this.appsModel.getAppById(utilityId);
      if (!utility) {
        throw new Error(`Utility ${utilityId} not found`);
      }

      const utilityDir = path.dirname(utility.local_path);

      // Load utility configuration
      const utilityConfig = this.fileOps.readJsonFile(utility.local_path);
      this.logger.info(`Loaded config from: ${utility.local_path}`);

      // Detect runtime type based on language field
      let runtimeType = 'python';
      let executablePath = null;

      if (runtimeType === 'python') {
        const pythonFile = 'main.py';
        runtimeType = 'python-spawn';
        executablePath = path.join(utilityDir, pythonFile);
      }  else {
        throw new Error(`Unsupported language: ${runtimeType}. Only 'python' and 'shell' are supported.`);
      }

      if (!executablePath) {
        throw new Error(`No executable file found for utility ${utilityId} (expected ${runtimeType} runtime)`);
      }

      this.logger.success(`Utility ${utilityId} detected as ${runtimeType} runtime`);

      // Record app run in database
      await this.appsModel.recordAppRun(utilityId);

      // Track analytics
      await this.analyticsModel.trackAppRun(utilityId, {
        runtime_type: runtimeType,
        has_file_data: !!fileData,
        config_provided: !!config
      });

      return {
        utilityId,
        runtimeType,
        executablePath,
        localPath: utility.local_path,
        config: utilityConfig,
        fileData,
        userConfig: config
      };

    } catch (error) {
      this.logger.error('Run utility error:', error);

      // Log the error
      await this.logsModel.logAppError(utilityId, utilityId, error);

      throw error;
    }
  }

  /**
   * Uninstall a utility by removing its files and database entry
   */
  async uninstallUtility(utilityId) {
    try {
      this.logger.start(`Uninstalling utility: ${utilityId}`);

      // Get utility from database
      const utility = await this.appsModel.getAppById(utilityId);
      if (!utility) {
        throw new Error(`Utility ${utilityId} not found`);
      }

      const utilityDir = path.dirname(utility.local_path);

      // Remove utility directory
      this.fileOps.removeDirectory(utilityDir);

      // Remove from database
      await this.appsModel.deleteApp(utilityId);

      // Log uninstallation
      await this.logsModel.logAppUninstall(utilityId, utility.name, {
        directory_removed: utilityDir
      });

      // Track analytics
      await this.analyticsModel.trackAppUninstall(utilityId, {
        app_name: utility.name,
        app_version: utility.version,
        language: utility.language
      });

      this.logger.complete(`Uninstalled utility: ${utilityId}`);
      return { success: true, utilityId };

    } catch (error) {
      this.logger.error('Uninstall utility error:', error);

      // Log the error
      await this.logsModel.logSystemError(error, {
        operation: 'uninstall_utility',
        utility_id: utilityId
      });

      throw error;
    }
  }

  // Helper methods for database access

  /**
   * Get all apps with optional filtering
   */
  async getAllApps(options = {}) {
    try {
      if (options.category) {
        return await this.appsModel.getAppsByCategory(options.category);
      }
      return await this.appsModel.getAllApps();
    } catch (error) {
      this.logger.error('Error getting all apps:', error);
      return [];
    }
  }

  /**
   * Search apps by query
   */
  async searchApps(query) {
    try {
      return await this.appsModel.searchApps(query);
    } catch (error) {
      this.logger.error('Error searching apps:', error);
      return [];
    }
  }

  /**
   * Get app statistics
   */
  async getAppStats() {
    try {
      return await this.appsModel.getAppStats();
    } catch (error) {
      this.logger.error('Error getting app stats:', error);
      return { total_apps: 0, active_apps: 0, total_runs: 0 };
    }
  }

  /**
   * Get analytics summary
   */
  async getAnalyticsSummary(timeRange = '7d') {
    try {
      return await this.analyticsModel.getAnalyticsSummary(timeRange);
    } catch (error) {
      this.logger.error('Error getting analytics summary:', error);
      return {};
    }
  }

  /**
   * Get recent logs
   */
  async getRecentLogs(limit = 20) {
    try {
      return await this.logsModel.getRecentLogs(limit);
    } catch (error) {
      this.logger.error('Error getting recent logs:', error);
      return [];
    }
  }

  /**
   * Clear all database data (for debug purposes)
   */
  async clearDatabase() {
    try {
      this.logger.warn('Clearing all database data');

      // Get all apps first
      const apps = await this.appsModel.getAllApps();

      // Remove all app directories
      for (const app of apps) {
        const utilityDir = path.dirname(app.local_path);
        this.fileOps.removeDirectory(utilityDir);
      }

      // Clear all database collections
      const { getDatabase } = require('../data/db');
      const db = getDatabase();

      await db.clearCollection('apps');
      await db.clearCollection('logs');
      await db.clearCollection('analytics');

      // Recreate utilities directory
      this.fileOps.ensureDirectory(this.utilitiesPath);

      this.logger.complete('Database cleared successfully');
      return { success: true };
    } catch (error) {
      this.logger.error('Error clearing database:', error);
      throw error;
    }
  }




}

module.exports = ContextBridge;
