const fs = require('fs');
const path = require('path');
const os = require('os');
const { spawn } = require('child_process');
const { logger, createLogger } = require('../../electron/logger');
const { app } = require('electron');

/**
 * PythonRunner - Handles Python utility execution using child_process.spawn()
 * Provides secure execution of Python utilities with dependency management
 */
class PythonRunner {
  constructor() {
    this.installedPackages = new Set();
    this.logDir = path.join(app.getPath('userData'), 'logs');
    this.logger = createLogger('PythonRunner');
    this.tempFiles = new Set(); // Track temp files for cleanup
    this.ensureLogDirectory();
    this.logger.python('PythonRunner initialized (spawn-based)');
  }

  /**
   * Ensure log directory exists
   */
  ensureLogDirectory() {
    try {
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
      }
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  /**
   * Robust logging method with proper type checking
   */
  log(message, level = 'info') {
    try {
      // Ensure message is a string
      const messageStr = typeof message === 'object' ? JSON.stringify(message) : String(message);

      // Ensure level is a string
      const levelStr = typeof level === 'string' ? level : 'info';

      // Use Signale for colorful console logging
      switch(levelStr.toLowerCase()) {
        case 'error':
          this.logger.error(messageStr);
          break;
        case 'warn':
        case 'warning':
          this.logger.warn(messageStr);
          break;
        case 'debug':
          this.logger.debug(messageStr);
          break;
        default:
          this.logger.python(messageStr);
      }

      // File logging with error handling
      try {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${levelStr.toUpperCase()}] ${messageStr}`;
        const logFile = path.join(this.logDir, 'python-runner.log');
        fs.appendFileSync(logFile, logMessage + '\n');
      } catch (fileError) {
        console.error('Failed to write to log file:', fileError);
      }
    } catch (error) {
      console.error('Logging error:', error);
    }
  }

  /**
   * Install Python package using pip
   */
  async installPackage(packageName) {
    if (this.installedPackages.has(packageName)) {
      this.log(`Package ${packageName} already installed, skipping`);
      return true;
    }

    return new Promise((resolve, reject) => {
      this.log(`Installing Python package: ${packageName}`);
      
      const pip = spawn('pip3', ['install', packageName]);
      let output = '';
      let error = '';

      pip.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        this.log(`pip stdout: ${text.trim()}`);
      });

      pip.stderr.on('data', (data) => {
        const text = data.toString();
        error += text;
        this.log(`pip stderr: ${text.trim()}`, 'warn');
      });

      pip.on('close', (code) => {
        if (code === 0) {
          this.installedPackages.add(packageName);
          this.log(`Successfully installed package: ${packageName}`);
          resolve(true);
        } else {
          this.log(`Failed to install package ${packageName}: ${error}`, 'error');
          reject(new Error(`pip install failed: ${error}`));
        }
      });
    });
  }

  /**
   * Install dependencies from config.json requirements
   */
  async installDependencies(requirements) {
    if (!requirements || !Array.isArray(requirements)) {
      this.log('No requirements to install');
      return;
    }

    this.log(`Installing ${requirements.length} dependencies: ${requirements.join(', ')}`);
    
    for (const packageName of requirements) {
      try {
        await this.installPackage(packageName);
      } catch (error) {
        this.log(`Failed to install ${packageName}: ${error.message}`, 'error');
        // Continue with other packages even if one fails
      }
    }
  }

  /**
   * Run Python utility using spawn - Enhanced with robust error handling
   */
  async runPythonUtility(scriptPath, inputData) {
    // Validation
    if (!scriptPath || !fs.existsSync(scriptPath)) {
      throw new Error(`Python script not found: ${scriptPath}`);
    }

    return new Promise((resolve, reject) => {
      const timeout = 30000; // 30 second timeout
      let timeoutId;

      this.log(`Executing Python script: ${scriptPath}`);
      this.log(`Input data: ${JSON.stringify(inputData).substring(0, 200)}...`);

      const python = spawn('python3', [scriptPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        timeout: timeout
      });

      let output = '';
      let error = '';
      let isResolved = false;

      // Set timeout
      timeoutId = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          python.kill('SIGTERM');
          reject(new Error(`Python script execution timed out after ${timeout}ms`));
        }
      }, timeout);

      // Send input data to Python script via stdin
      try {
        const inputJson = JSON.stringify(inputData);
        python.stdin.write(inputJson);
        python.stdin.end();
      } catch (stdinError) {
        clearTimeout(timeoutId);
        isResolved = true;
        reject(new Error(`Failed to send input to Python script: ${stdinError.message}`));
        return;
      }

      python.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        this.log(`Python stdout: ${text.trim()}`);
      });

      python.stderr.on('data', (data) => {
        const text = data.toString();
        error += text;
        this.log(`Python stderr: ${text.trim()}`, 'warn');
      });

      python.on('close', (code, signal) => {
        clearTimeout(timeoutId);

        if (isResolved) return;
        isResolved = true;

        this.log(`Python process exited with code: ${code}, signal: ${signal}`);

        if (code === 0) {
          try {
            const trimmedOutput = output.trim();
            if (!trimmedOutput) {
              resolve({ success: true, message: 'No output from Python script' });
              return;
            }

            // Try to parse output as JSON
            const result = JSON.parse(trimmedOutput);
            this.log('Python execution successful');
            resolve(result);
          } catch (parseError) {
            // If not JSON, return raw output
            this.log('Python output is not JSON, returning raw output');
            resolve({ success: true, output: output.trim() });
          }
        } else {
          const errorMessage = error.trim() || `Process exited with code ${code}`;
          this.log(`Python execution failed: ${errorMessage}`, 'error');
          reject(new Error(`Python execution failed: ${errorMessage}`));
        }
      });

      python.on('error', (err) => {
        clearTimeout(timeoutId);

        if (isResolved) return;
        isResolved = true;

        this.log(`Python process error: ${err.message}`, 'error');
        reject(new Error(`Failed to start Python process: ${err.message}`));
      });
    });
  }

  /**
   * Process file data with Python utility - Enhanced with robust error handling
   */
  async processFileData(scriptPath, fileData, config) {
    // Input validation
    if (!scriptPath || typeof scriptPath !== 'string') {
      throw new Error('Invalid script path provided');
    }

    if (!Array.isArray(fileData) || fileData.length === 0) {
      throw new Error('No file data provided or invalid format');
    }

    const tempFiles = [];

    try {
      this.log(`Processing ${fileData.length} files with Python utility`);

      // Create temporary files from file content
      for (let i = 0; i < fileData.length; i++) {
        const file = fileData[i];

        if (!file || !file.filename || !file.content) {
          this.log(`Skipping invalid file at index ${i}: missing filename or content`, 'warn');
          continue;
        }

        this.log(`Creating temp file for: ${file.filename}`);

        try {
          // Validate and convert content
          let buffer;
          if (Array.isArray(file.content)) {
            buffer = Buffer.from(file.content);
          } else if (Buffer.isBuffer(file.content)) {
            buffer = file.content;
          } else if (typeof file.content === 'string') {
            buffer = Buffer.from(file.content, 'utf8');
          } else {
            throw new Error(`Invalid content type for file ${file.filename}`);
          }

          // Create temp file
          const tempFile = await this.createTempFile(buffer, file.filename);
          tempFiles.push(tempFile);
          this.tempFiles.add(tempFile.path); // Track for global cleanup

          this.log(`Created temp file: ${tempFile.path}`);
        } catch (fileError) {
          this.log(`Failed to create temp file for ${file.filename}: ${fileError.message}`, 'error');
          throw fileError;
        }
      }

      if (tempFiles.length === 0) {
        throw new Error('No valid files could be processed');
      }

      // Prepare input data for Python script
      const inputData = {
        files: tempFiles.map(tempFile => ({
          path: tempFile.path,
          filePath: tempFile.path,
          name: tempFile.originalName,
          size: tempFile.size || 0
        })),
        config: this.sanitizeConfig(config)
      };

      this.log(`Prepared input data for ${tempFiles.length} files`);

      // Run Python utility with file paths
      const result = await this.runPythonUtility(scriptPath, inputData);

      // Parse and validate the result
      const parsedResult = this.parseUtilityResult(result);

      this.log('Python utility completed successfully');
      this.log(`Result: ${JSON.stringify(parsedResult).substring(0, 200)}...`, 'info');

      return parsedResult;

    } catch (error) {
      this.log(`Process file data error: ${error.message}`, 'error');
      throw error;
    } finally {
      // Clean up temporary files
      await this.cleanupTempFiles(tempFiles);
    }
  }

  /**
   * Sanitize configuration object
   */
  sanitizeConfig(config) {
    if (!config || typeof config !== 'object') {
      return {};
    }

    // Create a clean copy of config with only safe values
    const sanitized = {};
    for (const [key, value] of Object.entries(config)) {
      if (typeof key === 'string' && key.length > 0) {
        // Only allow primitive values
        if (['string', 'number', 'boolean'].includes(typeof value)) {
          sanitized[key] = value;
        } else if (value === null || value === undefined) {
          sanitized[key] = value;
        }
      }
    }

    return sanitized;
  }

  /**
   * Parse utility result with robust error handling
   */
  parseUtilityResult(result) {
    try {
      if (typeof result === 'object' && result !== null) {
        return result;
      }

      if (typeof result === 'string') {
        const trimmed = result.trim();
        if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
          return JSON.parse(trimmed);
        }
        return { success: true, output: trimmed };
      }

      return { success: true, result: result };
    } catch (parseError) {
      this.log(`Failed to parse utility result: ${parseError.message}`, 'warn');
      return {
        success: false,
        error: 'Invalid response format',
        message: String(result).substring(0, 500)
      };
    }
  }

  /**
   * Clean up temporary files with error handling
   */
  async cleanupTempFiles(tempFiles) {
    if (!Array.isArray(tempFiles)) {
      return;
    }

    for (const tempFile of tempFiles) {
      try {
        if (tempFile && tempFile.path && fs.existsSync(tempFile.path)) {
          fs.unlinkSync(tempFile.path);
          this.tempFiles.delete(tempFile.path);
          this.log(`Cleaned up temp file: ${tempFile.path}`);
        }
      } catch (cleanupError) {
        this.log(`Failed to cleanup temp file ${tempFile.path}: ${cleanupError.message}`, 'warn');
      }
    }
  }

  /**
   * Create temporary file from buffer - Enhanced with validation
   */
  async createTempFile(buffer, originalFilename) {
    if (!Buffer.isBuffer(buffer)) {
      throw new Error('Invalid buffer provided for temp file creation');
    }

    if (!originalFilename || typeof originalFilename !== 'string') {
      throw new Error('Invalid filename provided for temp file creation');
    }

    try {
      const tempDir = path.join(os.tmpdir(), 'fileduck-temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Generate unique filename
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 8);
      const extension = path.extname(originalFilename);
      const baseName = path.basename(originalFilename, extension);
      const tempFilename = `${baseName}_${timestamp}_${random}${extension}`;
      const tempFilePath = path.join(tempDir, tempFilename);

      // Write buffer to temp file
      fs.writeFileSync(tempFilePath, buffer);

      // Get file stats
      const stats = fs.statSync(tempFilePath);

      return {
        path: tempFilePath,
        originalName: originalFilename,
        tempName: tempFilename,
        size: stats.size
      };
    } catch (error) {
      this.log(`Failed to create temp file: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Save output to converted folder
   */
  async saveToConverted(content, filename) {
    try {
      const convertedDir = path.join(process.cwd(), 'converted');
      if (!fs.existsSync(convertedDir)) {
        fs.mkdirSync(convertedDir, { recursive: true });
      }

      const filePath = path.join(convertedDir, filename);
      fs.writeFileSync(filePath, content, 'utf8');
      this.log(`Saved output to: ${filePath}`);
      return filePath;
    } catch (error) {
      this.log(`Failed to save file: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Global cleanup method for all temp files
   */
  async globalCleanup() {
    this.log('Performing global cleanup of temporary files');

    for (const tempFilePath of this.tempFiles) {
      try {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
          this.log(`Cleaned up orphaned temp file: ${tempFilePath}`);
        }
      } catch (error) {
        this.log(`Failed to cleanup orphaned temp file ${tempFilePath}: ${error.message}`, 'warn');
      }
    }

    this.tempFiles.clear();

    // Also clean up the temp directory if empty
    try {
      const tempDir = path.join(os.tmpdir(), 'fileduck-temp');
      if (fs.existsSync(tempDir)) {
        const files = fs.readdirSync(tempDir);
        if (files.length === 0) {
          fs.rmdirSync(tempDir);
          this.log('Removed empty temp directory');
        }
      }
    } catch (error) {
      this.log(`Failed to remove temp directory: ${error.message}`, 'warn');
    }
  }

  /**
   * Destructor-like method for cleanup
   */
  async destroy() {
    await this.globalCleanup();
    this.log('PythonRunner destroyed');
  }
}

module.exports = PythonRunner;
