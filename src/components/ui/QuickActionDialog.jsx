import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from './dialog';
import { <PERSON><PERSON> } from './button';
import { Badge } from './badge';
import { Separator } from './separator';
import { FileIcon, PlayIcon, XIcon, FolderIcon } from 'lucide-react';
import { cn } from '../../lib/utils';

/**
 * QuickAction Dialog - Apple-style dialog for selecting apps to handle files
 * Follows macOS design guidelines with proper spacing and typography
 */
export function QuickActionDialog({ 
  isOpen, 
  onClose, 
  files = [], 
  compatibleApps = [], 
  onRunApp,
  onOpenFolder 
}) {
  const [selectedApp, setSelectedApp] = useState(null);
  const [isRunning, setIsRunning] = useState(false);

  // Auto-select first app if only one is available
  useEffect(() => {
    if (compatibleApps.length === 1 && !selectedApp) {
      setSelectedApp(compatibleApps[0]);
    }
  }, [compatibleApps, selectedApp]);

  const handleRunApp = async () => {
    if (!selectedApp) return;
    
    setIsRunning(true);
    try {
      await onRunApp(selectedApp, files);
      onClose();
    } catch (error) {
      console.error('Error running app:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getFileTypeColor = (extension) => {
    const colors = {
      '.csv': 'bg-green-100 text-green-800',
      '.json': 'bg-blue-100 text-blue-800',
      '.pdf': 'bg-red-100 text-red-800',
      '.png': 'bg-purple-100 text-purple-800',
      '.jpg': 'bg-purple-100 text-purple-800',
      '.jpeg': 'bg-purple-100 text-purple-800',
      '.mp4': 'bg-orange-100 text-orange-800',
      '.mp3': 'bg-yellow-100 text-yellow-800'
    };
    return colors[extension] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] p-0 gap-0 bg-white border border-gray-200 shadow-xl">
        {/* Header */}
        <DialogHeader className="px-6 py-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold text-gray-900 -apple-system">
              FileDuck QuickAction
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0 rounded-full hover:bg-gray-100"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="px-6 py-4 space-y-4">
          {/* Files Section */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700 -apple-system">
              Selected Files ({files.length})
            </h3>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {files.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 p-2 rounded-lg bg-gray-50 border border-gray-100"
                >
                  <FileIcon className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate -apple-system">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500 -apple-system">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={cn("text-xs font-medium", getFileTypeColor(file.extension))}
                  >
                    {file.extension.toUpperCase().substring(1)}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          <Separator className="bg-gray-100" />

          {/* Apps Section */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700 -apple-system">
              Compatible Apps ({compatibleApps.length})
            </h3>
            
            {compatibleApps.length === 0 ? (
              <div className="text-center py-8 space-y-3">
                <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                  <FileIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-900 -apple-system">
                    No Compatible Apps Found
                  </p>
                  <p className="text-xs text-gray-500 -apple-system">
                    Install apps from the Tool Store to handle these file types
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {compatibleApps.map((app) => (
                  <div
                    key={app.app_id}
                    className={cn(
                      "flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-all",
                      selectedApp?.app_id === app.app_id
                        ? "bg-blue-50 border-blue-200 ring-1 ring-blue-200"
                        : "bg-white border-gray-200 hover:bg-gray-50"
                    )}
                    onClick={() => setSelectedApp(app)}
                  >
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <PlayIcon className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 -apple-system">
                        {app.name}
                      </p>
                      <p className="text-xs text-gray-500 truncate -apple-system">
                        {app.description}
                      </p>
                      <div className="flex items-center gap-1 mt-1">
                        {app.supportedExtensions?.map((ext) => (
                          <Badge
                            key={ext}
                            variant="outline"
                            className="text-xs px-1.5 py-0.5 h-auto"
                          >
                            {ext.toUpperCase().substring(1)}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    {app.run_count > 0 && (
                      <div className="text-xs text-gray-400 -apple-system">
                        Used {app.run_count}x
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-100 bg-gray-50 flex items-center justify-between gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onOpenFolder && onOpenFolder()}
            className="text-gray-600 border-gray-200 hover:bg-white -apple-system"
          >
            <FolderIcon className="h-4 w-4 mr-2" />
            Open Folder
          </Button>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="text-gray-600 border-gray-200 hover:bg-white -apple-system"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleRunApp}
              disabled={!selectedApp || isRunning || compatibleApps.length === 0}
              className="bg-blue-600 hover:bg-blue-700 text-white -apple-system min-w-[80px]"
            >
              {isRunning ? (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
                  Running...
                </div>
              ) : (
                'Convert'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default QuickActionDialog;
