import React from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '../../lib/utils';

const AppleProgress = ({
  isActive = false,
  progress = 0,
  error = null,
  success = false,
  message = '',
  className
}) => {
  if (!isActive && !error && !success) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={cn("p-0", className)}
      style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", system-ui, sans-serif' }}
    >
      {error ? (
        <div className="flex items-center space-x-3 p-3 bg-[rgba(255,59,48,0.04)] border-[0.5px] border-[#FF3B30] rounded-[10px]">
          <AlertCircle className="h-5 w-5 text-[#FF3B30] flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <p className="text-[13px] font-[590] text-[#FF3B30] leading-[1.38462]">Conversion Failed</p>
            <p className="text-[13px] font-normal text-[#FF3B30] mt-1 leading-[1.38462]">{error}</p>
          </div>
        </div>
      ) : success ? (
        <div className="flex items-center space-x-3 p-3 bg-[rgba(52,199,89,0.04)] border-[0.5px] border-[#34C759] rounded-[10px]">
          <CheckCircle className="h-5 w-5 text-[#34C759] flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <p className="text-[13px] font-[590] text-[#34C759] leading-[1.38462]">Conversion Complete</p>
            {message && <p className="text-[13px] font-normal text-[#34C759] mt-1 leading-[1.38462]">{message}</p>}
          </div>
        </div>
      ) : isActive ? (
        <div className="space-y-3 p-3 bg-[#f2f2f7] border-[0.5px] border-[#d1d1d6] rounded-[10px]">
          <div className="flex items-center justify-between">
            <p className="text-[13px] font-[590] text-[#1d1d1f] leading-[1.38462]">
              {message || 'Converting files...'}
            </p>
            <span className="text-[13px] font-normal text-[#86868b] leading-[1.38462]">{Math.round(progress)}%</span>
          </div>

          <div className="w-full bg-[#e5e5ea] rounded-full h-1 overflow-hidden">
            <motion.div
              className="h-full bg-[#007AFF] rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            />
          </div>
        </div>
      ) : null}
    </motion.div>
  );
};

export { AppleProgress };
