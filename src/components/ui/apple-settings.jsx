import React from 'react';
import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { cn } from '../../lib/utils';

const AppleSettings = ({
  sections = [],
  onSettingChange,
  values = {},
  className
}) => {
  const handleChange = (key, value) => {
    onSettingChange?.(key, value);
  };

  const renderSettingItem = (item) => {
    const value = values[item.key] || item.defaultValue;

    switch (item.type) {
      case 'dropdown':
        return (
          <div key={item.key} className="space-y-1.5">
            <label className="block text-[13px] font-[590] text-[#1d1d1f] mb-1.5 leading-[1.38462]">
              {item.label}
              {item.required && <span className="text-[#FF3B30] ml-1">*</span>}
            </label>
            <div className="relative">
              <select
                value={value || ''}
                onChange={(e) => handleChange(item.key, e.target.value)}
                className={cn(
                  "w-full h-8 px-2 pr-7 text-[13px] font-normal text-[#1d1d1f] bg-white border-[0.5px] border-[#d1d1d6] rounded-md",
                  "focus:outline-none focus:ring-[3px] focus:ring-[rgba(0,122,255,0.2)] focus:border-[#007AFF]",
                  "appearance-none cursor-pointer transition-all duration-150",
                  "leading-[1.38462]"
                )}
                style={{
                  paddingTop: '5px',
                  paddingBottom: '6px',
                  backgroundImage: `url("data:image/svg+xml,%3csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M1 1L5 5L9 1' stroke='%2386868B' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e")`,
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'right 8px center'
                }}
              >
                {item.placeholder && (
                  <option value="" disabled>
                    {item.placeholder}
                  </option>
                )}
                {item.options?.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        );

      case 'slider':
        return (
          <div key={item.key} className="space-y-1.5">
            <label className="block text-[13px] font-[590] text-[#1d1d1f] mb-1.5 leading-[1.38462]">
              {item.label}
              {item.required && <span className="text-[#FF3B30] ml-1">*</span>}
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="range"
                min={item.min || 0}
                max={item.max || 100}
                step={item.step || 1}
                value={value || item.min || 0}
                onChange={(e) => handleChange(item.key, parseInt(e.target.value))}
                className={cn(
                  "flex-1 h-1 bg-[#e5e5ea] rounded-full appearance-none cursor-pointer",
                  "focus:outline-none focus:ring-[3px] focus:ring-[rgba(0,122,255,0.2)]"
                )}
                style={{
                  background: `linear-gradient(to right, #007AFF 0%, #007AFF ${((value - (item.min || 0)) / ((item.max || 100) - (item.min || 0))) * 100}%, #e5e5ea ${((value - (item.min || 0)) / ((item.max || 100) - (item.min || 0))) * 100}%, #e5e5ea 100%)`,
                  WebkitAppearance: 'none',
                  height: '4px'
                }}
              />
              <span className="text-[13px] font-normal text-[#86868b] min-w-[3rem] text-right leading-[1.38462]">
                {value || item.min || 0}{item.unit || ''}
              </span>
            </div>
            <style jsx>{`
              input[type="range"]::-webkit-slider-thumb {
                appearance: none;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: white;
                border: 2px solid #007AFF;
                cursor: pointer;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              }
              input[type="range"]::-moz-range-thumb {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: white;
                border: 2px solid #007AFF;
                cursor: pointer;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              }
            `}</style>
          </div>
        );

      case 'input':
        return (
          <div key={item.key} className="space-y-1.5">
            <label className="block text-[13px] font-[590] text-[#1d1d1f] mb-1.5 leading-[1.38462]">
              {item.label}
              {item.required && <span className="text-[#FF3B30] ml-1">*</span>}
            </label>
            <input
              type={item.inputType || 'text'}
              value={value || ''}
              onChange={(e) => handleChange(item.key, e.target.value)}
              placeholder={item.placeholder}
              className={cn(
                "w-full h-7 px-2 text-[13px] font-normal text-[#1d1d1f] bg-white border-[0.5px] border-[#d1d1d6] rounded-md",
                "focus:outline-none focus:ring-[3px] focus:ring-[rgba(0,122,255,0.2)] focus:border-[#007AFF]",
                "placeholder-[#86868b] transition-all duration-150 leading-[1.38462]"
              )}
              style={{
                paddingTop: '5px',
                paddingBottom: '6px'
              }}
            />
          </div>
        );

      case 'switch':
        return (
          <div key={item.key} className="flex items-center justify-between">
            <label className="text-[13px] font-[590] text-[#1d1d1f] leading-[1.38462]">
              {item.label}
              {item.required && <span className="text-[#FF3B30] ml-1">*</span>}
            </label>
            <motion.button
              onClick={() => handleChange(item.key, !value)}
              className={cn(
                "relative inline-flex h-6 w-[42px] items-center rounded-full transition-colors duration-200 cursor-pointer",
                value ? "bg-[#34C759]" : "bg-[#e5e5ea]"
              )}
              whileTap={{ scale: 0.98 }}
            >
              <motion.span
                className="inline-block h-5 w-5 transform rounded-full bg-white shadow-[0_1px_3px_rgba(0,0,0,0.2)] transition-transform duration-200"
                animate={{
                  x: value ? 20 : 2
                }}
                style={{
                  position: 'absolute',
                  top: '2px'
                }}
              />
            </motion.button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn("mt-8 ml-8 mr-8 p-0", className)} style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", system-ui, sans-serif' }}>
      {sections.map((section, sectionIndex) => (
        <div key={sectionIndex} className={cn(sectionIndex > 0 && "mt-8")}>
          {section.title && (
            <h3 className="text-[15px] font-[590] text-[#1d1d1f] mb-4 leading-[1.26667]">
              {section.title}
            </h3>
          )}

          <div className="space-y-4">
            {section.rows.map((row, rowIndex) => (
              <div
                key={rowIndex}
                className="grid gap-5"
                style={{
                  gridTemplateColumns: `repeat(${row.items.length}, 1fr)`
                }}
              >
                {row.items.map(renderSettingItem)}
              </div>
            ))}
          </div>

          {sectionIndex < sections.length - 1 && (
            <div className="mt-8 border-b border-[#d1d1d6]" style={{ borderWidth: '0.5px' }} />
          )}
        </div>
      ))}
    </div>
  );
};

export { AppleSettings };
