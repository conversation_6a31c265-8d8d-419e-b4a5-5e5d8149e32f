import React, { useState, useEffect } from 'react';
import { Typography } from '../utils/typography';

const DynamicGreeting = ({ name = "Am<PERSON>" }) => {
  const [greeting, setGreeting] = useState({ title: '', subtitle: '' });

  // Title messages pool - FileDuck home screen welcome (chef's kiss edition)
  const titleMessages = [
    `Hey ${name}, let’s crush your file game today!`,
    `Morning ${name}, time to turn files into wins!`,
    `Yo ${name}, your files just met their match.`,
    `${name}, ready to power up your workflow?`,
    `Welcome back ${name}, let’s make magic with files!`,
    `${name}, fast files, big vibes.`,
    `Hey ${name}, let’s make file work feel like a breeze.`,
    `Let’s do this, ${name}! Files won’t know what hit ‘em.`,
    `${name}, your file sidekick is locked and loaded.`,
    `Rise and shine, ${name}, time to own those files.`,
  ];

// Subtitle messages pool - FileDuck home screen welcome (chef's kiss edition)
  const subtitleMessages = [
    "Convert, compress, and conquer — all in a snap.",
    "Less hassle, more hustle — that’s the FileDuck way.",
    "Right-click, relax, and watch the magic happen.",
    "File tasks done fast, so you can keep the momentum.",
    "FileDuck: where productivity meets joy.",
    "Turn file chaos into smooth sailing.",
    "Small clicks, huge results — feel the flow.",
    "Your digital sidekick for file domination.",
    "Speed, smarts, and a splash of fun.",
    "Files handled, so you can focus on your next big move.",
    "Workflow upgraded. Motivation unlocked.",
    "Simplify, energize, and get stuff done.",
    "File work? Consider it handled with style.",
    "More flow, less friction — let’s go!",
    "Where every click sparks progress.",
    "FileDuck’s got your back — and your files.",
    "Smart, swift, and ready to roll.",
    "Because you deserve file freedom.",
    "Crush your day, one file at a time.",
    "Welcome to your new productivity boost."
  ];

  const getTimeOfDay = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 21) return 'evening';
    return 'night';
  };

  const getDayOfWeek = () => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[new Date().getDay()];
  };

  const getRandomMessage = (messages) => {
    return messages[Math.floor(Math.random() * messages.length)];
  };

  const generateGreeting = () => {
    const timeOfDay = getTimeOfDay();
    const dayOfWeek = getDayOfWeek();
    
    // Filter title messages based on time of day for better relevance
    let relevantTitles = titleMessages;
    if (timeOfDay === 'morning') {
      relevantTitles = titleMessages.filter(msg => 
        msg.includes('morning') || msg.includes('Morning') || msg.includes('Rise') || msg.includes('grind')
      );
    } else if (timeOfDay === 'afternoon') {
      relevantTitles = titleMessages.filter(msg => 
        msg.includes('afternoon') || msg.includes('Afternoon') || msg.includes('hustle') || msg.includes('build')
      );
    } else if (timeOfDay === 'evening') {
      relevantTitles = titleMessages.filter(msg => 
        msg.includes('evening') || msg.includes('Evening') || msg.includes('strong')
      );
    }
    
    // Fallback to all messages if filtered list is too small
    if (relevantTitles.length < 3) {
      relevantTitles = titleMessages;
    }

    // Get day-specific subtitle or random one
    let relevantSubtitles = subtitleMessages.filter(msg => 
      msg.includes(dayOfWeek)
    );
    
    // Fallback to all subtitles if no day-specific ones
    if (relevantSubtitles.length === 0) {
      relevantSubtitles = subtitleMessages.filter(msg => 
        !msg.includes('Monday') && !msg.includes('Tuesday') && 
        !msg.includes('Wednesday') && !msg.includes('Thursday') && 
        !msg.includes('Friday') && !msg.includes('Saturday') && 
        !msg.includes('Sunday')
      );
    }

    return {
      title: getRandomMessage(relevantTitles),
      subtitle: getRandomMessage(relevantSubtitles)
    };
  };

  useEffect(() => {
    setGreeting(generateGreeting());
  }, [name]);

  return (
    <div className="mb-8">
      <h1 className={`${Typography.title1} mb-2`}>
        {greeting.title}
      </h1>
      <p className={`${Typography.bodySecondary} text-lg`}>
        {greeting.subtitle}
      </p>
    </div>
  );
};

export default DynamicGreeting;
