import React, { useState, useEffect } from 'react';
import { Typography } from '../utils/typography';

const DynamicGreeting = ({ name = "Am<PERSON>" }) => {
  const [greeting, setGreeting] = useState({ title: '', subtitle: '' });

  // Title messages pool (20+ variations)
  const titleMessages = [
    // Morning vibes
    `Hey ${name}, good morning! ☀️`,
    `Morning ${name}! Let's make today epic 🚀`,
    `Rise and grind, ${name}! 💪`,
    `Good morning ${name}! Time to slay 🔥`,
    `Morning vibes, ${name}! ✨`,
    `Yo ${name}! Let's crush this morning 💥`,
    `Good morning ${name}! Ready to ship? 🚢`,
    
    // Afternoon energy
    `Hey ${name}! Afternoon hustle time 🎯`,
    `What's good, ${name}? Let's build 🛠️`,
    `Afternoon ${name}! Keep the momentum 📈`,
    `Hey ${name}! Time to make magic happen ✨`,
    `Sup ${name}! Let's get productive 💼`,
    `Afternoon grind, ${name}! 🔧`,
    
    // Evening chill
    `Evening ${name}! Still going strong 🌟`,
    `Hey ${name}! Evening productivity mode 🌙`,
    `Good evening ${name}! Let's wrap up strong 💪`,
    `Evening ${name}! Time to finish what we started 🎯`,
    
    // Universal vibes
    `What's up ${name}! Ready to create? 🎨`,
    `Hey ${name}! Let's do something amazing 🚀`,
    `Yo ${name}! Time to level up 📊`,
    `Hey ${name}! Let's make it happen 💫`,
    `What's good ${name}! Ready to build? 🏗️`,
    `Hey ${name}! Let's ship something cool 📦`,
    `Sup ${name}! Time to innovate 💡`
  ];

  // Subtitle messages pool (20+ variations)
  const subtitleMessages = [
    // Day-specific motivation
    "It's Monday — fresh start, fresh wins 🎯",
    "Tuesday energy — let's keep building 🔨",
    "Wednesday vibes — halfway to greatness 📈",
    "Thursday momentum — almost there! 🚀",
    "Friday feels — let's finish strong 💪",
    "Saturday hustle — weekend productivity 🌟",
    "Sunday prep — setting up for success 📋",
    
    // General motivation
    "Perfect day to ship something amazing 📦",
    "Time to turn ideas into reality ✨",
    "Ready to make some digital magic? 🎨",
    "Let's build something people will love ❤️",
    "Today's the day to level up 📊",
    "Time to create, innovate, and dominate 🔥",
    "Ready to solve problems like a pro? 🧠",
    "Let's make productivity look effortless 💫",
    "Time to turn coffee into code ☕",
    "Ready to make the impossible possible? 🚀",
    "Let's create something worth sharing 📢",
    "Time to build the future, one tool at a time 🛠️",
    "Ready to make today legendary? 🏆",
    "Let's turn workflows into art 🎭",
    "Time to optimize everything 📈",
    "Ready to make technology work for you? ⚡",
    "Let's automate the boring stuff 🤖",
    "Time to make file management fun 📁",
    "Ready to streamline your digital life? 💻"
  ];

  const getTimeOfDay = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 21) return 'evening';
    return 'night';
  };

  const getDayOfWeek = () => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[new Date().getDay()];
  };

  const getRandomMessage = (messages) => {
    return messages[Math.floor(Math.random() * messages.length)];
  };

  const generateGreeting = () => {
    const timeOfDay = getTimeOfDay();
    const dayOfWeek = getDayOfWeek();
    
    // Filter title messages based on time of day for better relevance
    let relevantTitles = titleMessages;
    if (timeOfDay === 'morning') {
      relevantTitles = titleMessages.filter(msg => 
        msg.includes('morning') || msg.includes('Morning') || msg.includes('Rise') || msg.includes('grind')
      );
    } else if (timeOfDay === 'afternoon') {
      relevantTitles = titleMessages.filter(msg => 
        msg.includes('afternoon') || msg.includes('Afternoon') || msg.includes('hustle') || msg.includes('build')
      );
    } else if (timeOfDay === 'evening') {
      relevantTitles = titleMessages.filter(msg => 
        msg.includes('evening') || msg.includes('Evening') || msg.includes('strong')
      );
    }
    
    // Fallback to all messages if filtered list is too small
    if (relevantTitles.length < 3) {
      relevantTitles = titleMessages;
    }

    // Get day-specific subtitle or random one
    let relevantSubtitles = subtitleMessages.filter(msg => 
      msg.includes(dayOfWeek)
    );
    
    // Fallback to all subtitles if no day-specific ones
    if (relevantSubtitles.length === 0) {
      relevantSubtitles = subtitleMessages.filter(msg => 
        !msg.includes('Monday') && !msg.includes('Tuesday') && 
        !msg.includes('Wednesday') && !msg.includes('Thursday') && 
        !msg.includes('Friday') && !msg.includes('Saturday') && 
        !msg.includes('Sunday')
      );
    }

    return {
      title: getRandomMessage(relevantTitles),
      subtitle: getRandomMessage(relevantSubtitles)
    };
  };

  useEffect(() => {
    setGreeting(generateGreeting());
  }, [name]);

  return (
    <div className="mb-8">
      <h1 className={`${Typography.title1} mb-2`}>
        {greeting.title}
      </h1>
      <p className={`${Typography.bodySecondary} text-lg`}>
        {greeting.subtitle}
      </p>
    </div>
  );
};

export default DynamicGreeting;
