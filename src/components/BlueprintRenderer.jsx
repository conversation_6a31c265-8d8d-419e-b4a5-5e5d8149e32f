import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

// Import core UI components for SDK
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import { FileDropZone } from './ui/file-drop-zone';
import { ConvertedFilesDisplay } from './ui/converted-files-display';
import { ConversionProgress } from './ui/conversion-progress';
import { AppleDropZone } from './ui/apple-drop-zone';
import { AppleSettings } from './ui/apple-settings';
import { AppleProgress } from './ui/apple-progress';

// Import Lucide icons
import * as LucideIcons from 'lucide-react';
import { logger } from '../utils/logger';

const BlueprintRenderer = ({ config, utilityId, onExecute }) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({});
  const [files, setFiles] = useState([]);
  const [convertedFiles, setConvertedFiles] = useState([]);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [error, setError] = useState(null);
  const [uiComponent, setUiComponent] = useState(null);
  const [uiLoading, setUiLoading] = useState(true);

  // Load main.jsx component dynamically
  useEffect(() => {
    const loadUIComponent = async () => {
      try {
        setUiLoading(true);
        logger.debug('Loading UI component for utility:', utilityId);

        // Try to load main.jsx from the utility
        const uiComponentCode = await window.electronAPI.getUtilityUIComponent(utilityId);

        if (uiComponentCode) {
          logger.debug('UI component code loaded successfully');
          setUiComponent(uiComponentCode);
        } else {
          logger.warn('No main.jsx found for utility:', utilityId);
          setUiComponent(null);
        }
      } catch (error) {
        logger.error('Failed to load UI component:', error);
        setUiComponent(null);
      } finally {
        setUiLoading(false);
      }
    };

    loadUIComponent();
  }, [utilityId]);

  // Show loading state while UI component is being loaded
  if (uiLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-8">
        <div className="text-center space-y-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600">Loading app interface...</p>
        </div>
      </div>
    );
  }

  // Check if UI component exists
  if (!uiComponent) {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-8">
        <div className="text-center space-y-6">
          <h3 className="text-2xl font-bold text-gray-900">
            No UI Layout Found
          </h3>
          <p className="text-gray-600 max-w-md mx-auto">
            This app doesn't have a UI configuration yet.
          </p>
          <Button
            onClick={() => navigate('/')}
            variant="outline"
            className="mt-4"
          >
            <LucideIcons.ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Button>
        </div>
      </div>
    );
  }

  // Component mapping for SDK-compliant dynamic rendering
  const componentMap = {
    // Layout components
    'div': 'div',
    'section': 'section',
    'main': 'main',

    // Core UI components (SDK-approved)
    'Button': Button,
    'Card': Card,
    'CardContent': CardContent,
    'CardDescription': CardDescription,
    'CardHeader': CardHeader,
    'CardTitle': CardTitle,
    'Select': Select,
    'SelectContent': SelectContent,
    'SelectItem': SelectItem,
    'SelectTrigger': SelectTrigger,
    'SelectValue': SelectValue,
    'Switch': Switch,
    'Label': Label,

    // File conversion components (SDK core)
    'FileDropZone': FileDropZone,
    'ConvertedFilesDisplay': ConvertedFilesDisplay,
    'ConversionProgress': ConversionProgress,

    // Apple Design Components
    'AppleDropZone': AppleDropZone,
    'AppleSettings': AppleSettings,
    'AppleProgress': AppleProgress,

    // Motion components
    'motion.div': motion.div,

    // Text elements
    'h1': 'h1',
    'h2': 'h2',
    'h3': 'h3',
    'h4': 'h4',
    'p': 'p',
    'span': 'span',

    // Icons (Lucide React - SDK approved)
    ...LucideIcons
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      setError('Please select files to convert');
      return;
    }

    setIsConverting(true);
    setError(null);
    setConversionProgress(0);

    try {
      // Prepare file data for Python utility execution
      // Convert File objects to proper data structure with content
      const fileData = await Promise.all(files.map(async (file) => {
        // Read file content as ArrayBuffer then convert to base64
        const arrayBuffer = await file.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);
        const content = Array.from(uint8Array);

        return {
          filename: file.name,
          content: content, // File content as byte array
          size: file.size,
          type: file.type || 'application/octet-stream'
        };
      }));

      // Execute the utility with proper data structure
      const result = await onExecute(fileData, formData);

      if (result.success) {
        // Handle successful conversion results
        const processedFiles = result.results?.map(r => ({
          filename: r.input_file,
          filePath: r.result?.output_file,
          success: r.result?.success,
          message: r.result?.message
        })) || [];

        setConvertedFiles(processedFiles);
        setConversionProgress(100);
      } else {
        setError(result.message || result.error || 'Conversion failed');
      }
    } catch (err) {
      logger.error('Conversion error:', err);
      setError(err.message || 'An error occurred during conversion');
    } finally {
      setIsConverting(false);
    }
  };

  const handleOpenFolder = async () => {
    if (window.electronAPI?.openFolder) {
      try {
        const folderPath = process.cwd ? `${process.cwd()}/converted` : './converted';
        await window.electronAPI.openFolder(folderPath);
      } catch (error) {
        logger.error('Failed to open folder:', error);
      }
    }
  };

  // Function to render the CSV to PDF converter with Apple design
  const renderCSVToPDFConverter = () => {
    // Handle file removal
    const handleFileRemove = (index) => {
      const newFiles = files.filter((_, i) => i !== index);
      setFiles(newFiles);
    };

    // Handle converted file click (open in Finder)
    const handleConvertedFileClick = (file) => {
      if (file.filePath) {
        handleOpenFolder(file.filePath);
      }
    };

    // Settings configuration for AppleSettings component
    const settingsSections = [
      {
        title: "PDF Configuration",
        rows: [
          {
            items: [
              {
                type: 'dropdown',
                key: 'page_size',
                label: 'Page Size',
                defaultValue: 'letter',
                options: [
                  { label: 'Letter (8.5 x 11 in)', value: 'letter' },
                  { label: 'A4 (210 x 297 mm)', value: 'a4' }
                ]
              },
              {
                type: 'dropdown',
                key: 'font_size',
                label: 'Font Size',
                defaultValue: '10',
                options: [
                  { label: '8pt (Small)', value: '8' },
                  { label: '10pt (Normal)', value: '10' },
                  { label: '12pt (Large)', value: '12' },
                  { label: '14pt (Extra Large)', value: '14' }
                ]
              }
            ]
          },
          {
            items: [
              {
                type: 'switch',
                key: 'include_header',
                label: 'Include column headers in PDF',
                defaultValue: true
              }
            ]
          }
        ]
      }
    ];

    // Handle settings change
    const handleSettingChange = (key, value) => {
      if (key === 'font_size') {
        setFormData({ ...formData, [key]: parseInt(value) });
      } else {
        setFormData({ ...formData, [key]: value });
      }
    };

    return (
      <div className="space-y-8">
        {/* Drop Zone with Convert Button and Converted Files */}
        <AppleDropZone
          maxFiles={10}
          acceptedFileTypes={['.csv']}
          onFilesAdded={setFiles}
          onConvert={handleConvert}
          onFileRemove={handleFileRemove}
          onConvertedFileClick={handleConvertedFileClick}
          isConverting={isConverting}
          convertedFiles={convertedFiles}
          selectedFiles={files}
          errorMessage={error}
        />

        {/* Progress Indicator */}
        <AppleProgress
          isActive={isConverting}
          progress={conversionProgress}
          error={error}
          success={convertedFiles.length > 0 && !isConverting && !error}
          message={isConverting ? 'Converting CSV files to PDF...' : ''}
        />

        {/* Settings */}
        <AppleSettings
          sections={settingsSections}
          onSettingChange={handleSettingChange}
          values={formData}
        />
      </div>
    );
  };

  try {
    // Render the UI from main.jsx component
    if (uiComponent) {
      // For now, directly render the CSV to PDF converter
      // TODO: Implement proper dynamic JSX parsing in the future
      return renderCSVToPDFConverter();
    }

    // This should never be reached due to the check at the top
    return null;
  } catch (error) {
    logger.error('UI rendering error:', error);
    return (
      <div className="flex items-center justify-center min-h-[400px] p-8">
        <div className="text-center space-y-6">
          <div className="text-red-500 mb-4">
            <LucideIcons.AlertCircle className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900">
            UI Rendering Error
          </h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Failed to render the app interface: {error.message}
          </p>
          <Button
            onClick={() => navigate('/')}
            variant="outline"
            className="mt-4"
          >
            <LucideIcons.ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Button>
        </div>
      </div>
    );
  }
};

export default BlueprintRenderer;
