import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { ArrowLeft } from 'lucide-react';
import { motion } from 'framer-motion';
import BlueprintRenderer from './BlueprintRenderer';
import { logger } from '../utils/logger';

const UtilityRunnerView = () => {
  const { utilityId } = useParams();
  const navigate = useNavigate();
  const [appConfig, setAppConfig] = useState(null);
  const [loading, setLoading] = useState(true);

  // Load utility configuration
  useEffect(() => {
    const loadUtilityConfig = async () => {
      try {
        setLoading(true);
        logger.debug('Loading utility config for ID:', utilityId);

        if (window.electronAPI) {
          // Get installed utilities to find the one we need
          const utilities = await window.electronAPI.getInstalledUtilities();
          logger.debug('All installed utilities:', utilities);

          // Try to find utility by different ID formats
          let utility = utilities.find(u => u.id === utilityId || u.app_id === utilityId);

          if (!utility) {
            // Try partial matching for debugging
            utility = utilities.find(u =>
              u.id?.includes(utilityId) ||
              u.app_id?.includes(utilityId) ||
              utilityId.includes(u.id) ||
              utilityId.includes(u.app_id)
            );
            if (utility) {
              logger.debug('Found utility with partial match:', utility);
            }
          }

          if (utility) {
            logger.debug('Found utility:', utility);
            // Get utility info which now includes the config
            const utilityInfo = await window.electronAPI.getUtilityInfo(utility.app_id || utility.id);
            logger.debug('Utility info from database:', utilityInfo);

            if (utilityInfo && utilityInfo.config) {
              logger.debug('Utility config loaded:', utilityInfo.config);
              setAppConfig(utilityInfo.config);
            } else {
              logger.warn('No config found in utility info for:', utility.app_id || utility.id);
            }
          } else {
            logger.warn('Utility not found. Available utilities:', utilities.map(u => ({ id: u.id, app_id: u.app_id, name: u.name })));
          }
        }
      } catch (error) {
        logger.error('Failed to load utility config:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUtilityConfig();
  }, [utilityId]);

  // Show loading state
  if (loading) {
    return (
      <div className="p-8 h-full overflow-y-auto">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center space-x-4 mb-8">
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </div>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading utility...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if no config found
  if (!appConfig) {
    return (
      <div className="p-8 h-full overflow-y-auto">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center space-x-4 mb-8">
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </div>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <h3 className="text-2xl font-bold text-gray-900">
                Utility Not Found
              </h3>
              <p className="text-gray-600">
                The requested utility could not be loaded.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render the utility with BlueprintRenderer
  return (
    <div className="p-8 h-full overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {appConfig.name || 'FileDuckUtility'}
          </Button>
        </div>

        {/*/!* App Header *!/*/}
        {/*<div className="flex items-center gap-4 mb-8">*/}
        {/*  <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center">*/}
        {/*    <span className="text-2xl text-white">🔧</span>*/}
        {/*  </div>*/}
        {/*  <div>*/}
        {/*    <h1 className="text-3xl font-bold text-gray-900">*/}
        {/*      {appConfig.name || 'Utility'}*/}
        {/*    </h1>*/}
        {/*    <p className="text-gray-600">*/}
        {/*      {appConfig.description || 'FileDuck utility'}*/}
        {/*    </p>*/}
        {/*  </div>*/}
        {/*</div>*/}

        {/* Dynamic UI Renderer */}
        <BlueprintRenderer
          config={appConfig}
          utilityId={utilityId}
          onExecute={async (fileData, config) => {
            try {
              logger.debug('Executing utility with data:', { fileData, config });
              const result = await window.electronAPI.runUtility(utilityId, fileData, config);
              logger.debug('Utility execution result:', result);
              return result;
            } catch (error) {
              logger.error('Utility execution error:', error);
              throw error;
            }
          }}
        />
      </div>
    </div>
  );
};

export default UtilityRunnerView;
