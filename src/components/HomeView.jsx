import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Image,
  FileText,
  File,
  Music,
  Video,
  Settings,
  Info,
  Trash2
} from 'lucide-react';
import { PageWrapper } from './ui/page-wrapper';
import { MacOSButton } from './ui/macos-button';
import { Typography } from '../utils/typography';
import { logger } from '../utils/logger';
import { AppDetailsDialog, UninstallConfirmDialog } from './ui/app-details-dialog';
import DynamicGreeting from './DynamicGreeting';

// AppCard component for installed apps - Apple App Store style
const AppCard = ({ app, showDivider = true, onOpen, onUninstall, onDetails }) => {
  const IconComponent = app.icon;

  return (
    <div className="relative">
      <div className="flex items-center py-3 px-0">
        {/* App Icon */}
        <div className="w-16 h-16 bg-system-blue/10 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
          <IconComponent className="h-8 w-8 text-system-blue" />
        </div>

        {/* App Content */}
        <div className="flex-1 min-w-0 mr-3">
          <h3 className={`${Typography.body} font-medium mb-1 truncate`}>
            {app.name}
          </h3>
          <p className={`${Typography.caption} mb-2 truncate`}>
            {app.description}
          </p>
          <div className="flex items-center space-x-3 text-xs">
            <motion.button
                onClick={() => onDetails(app.id)}
                className="bg-macos-elevated hover:bg-macos-border text-macos-text-secondary rounded-full p-1.5 focus:outline-none transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
            >
              <Info className="h-4 w-4" />
            </motion.button>
            <motion.button
                onClick={() => onUninstall(app)}
                className="bg-system-red/10 hover:bg-system-red/20 text-system-red rounded-full p-1.5 focus:outline-none transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
            >
              <Trash2 className="h-4 w-4" />
            </motion.button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex-shrink-0 flex items-center space-x-2">
          <MacOSButton
            onClick={() => onOpen(app.id)}
            variant="secondary"
            size="sm"
            className="rounded-full px-5"
          >
            Open
          </MacOSButton>
        </div>
      </div>

      {/* Divider Line */}
      {showDivider && (
        <div className="absolute bottom-0 left-20 right-0 h-px bg-macos-border"></div>
      )}
    </div>
  );
};

const HomeView = () => {
  const navigate = useNavigate();
  const [installedApps, setInstalledApps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedApp, setSelectedApp] = useState(null);
  const [showAppDetails, setShowAppDetails] = useState(false);
  const [showUninstallConfirm, setShowUninstallConfirm] = useState(false);
  const [appToUninstall, setAppToUninstall] = useState(null);

  // Icon mapping for different app categories
  const getIconForCategory = (category) => {
    const iconMap = {
      'Image Processing': Image,
      'File Conversion': FileText,
      'Document': File,
      'Media': Video,
      'Audio': Music,
      'Utility': Settings,
      'default': Settings
    };
    return iconMap[category] || iconMap.default;
  };

  // Load installed apps from database
  const loadInstalledApps = async () => {
    try {
      if (window.electronAPI) {
        const utilities = await window.electronAPI.getInstalledUtilities();
        setInstalledApps(utilities.map((utility) => ({
          id: utility.id || utility.app_id,
          name: utility.name,
          version: utility.version,
          installDate: utility.installDate || utility.installed_at,
          description: utility.description,
          category: utility.category || 'Utility', // Default category for utilities
          icon: getIconForCategory(utility.category || 'Utility'),
          runCount: utility.run_count || 0,
          lastRunAt: utility.last_run_at
        })));
      }
    } catch (error) {
      logger.error('Failed to load installed apps:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInstalledApps();
  }, []);

  const handleAppOpen = (appId) => {
    // Remember that we came from Home tab
    sessionStorage.setItem('sourceTab', '/');
    navigate(`/app/${appId}`);
  };

  const handleAppUninstall = (app) => {
    logger.info(`Showing uninstall confirmation for app: ${app.name} (${app.id})`);
    setAppToUninstall(app);
    setShowUninstallConfirm(true);
  };

  const handleConfirmUninstall = async (appId) => {
    try {
      logger.start(`Starting uninstall process for app: ${appId}`);

      if (window.electronAPI && window.electronAPI.uninstallUtility) {
        const result = await window.electronAPI.uninstallUtility(appId);
        if (result.success) {
          // Call uninstall API for analytics
          await callUninstallAPI(appId);

          // Refresh the installed apps list
          await loadInstalledApps();
          logger.success(`App ${appId} uninstalled successfully`);

          // Close dialogs
          setShowUninstallConfirm(false);
          setShowAppDetails(false);
          setAppToUninstall(null);
          setSelectedApp(null);
        } else {
          logger.error('Failed to uninstall app:', result.error);
        }
      } else {
        logger.info('Uninstall functionality only available in desktop app');
      }
    } catch (error) {
      logger.error('Error uninstalling app:', error);
    }
  };

  const handleAppDetails = (appId) => {
    logger.info(`Showing app details for: ${appId}`);
    const app = installedApps.find(a => a.id === appId);
    if (app) {
      setSelectedApp(app);
      setShowAppDetails(true);
    } else {
      logger.error(`App not found: ${appId}`);
    }
  };

  // API call for uninstall analytics
  const callUninstallAPI = async (appId) => {
    try {
      const API_BASE_URL = 'https://api.fileduck.com';
      const USER_ID = 'user_test_123';

      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/${appId}/uninstall`, {
        method: 'POST',
        headers: {
          'user-id': USER_ID,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) throw new Error('Failed to record uninstallation');
      const result = await response.json();
      logger.info('Uninstall API call successful:', result);
      return result;
    } catch (error) {
      logger.error('Uninstall API call failed:', error);
    }
  };

  return (
    <PageWrapper className="px-8 pt-16 pb-8 h-full overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        {/* Dynamic Greeting */}
        <DynamicGreeting name="Amin" />

        {/* My apps Section */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <h2 className={Typography.title2}>My Apps</h2>
            <MacOSButton
              onClick={() => navigate('/store')}
              variant="primary"
              className="rounded-xl"
            >
              Browse Tool Store
            </MacOSButton>
          </div>

          {installedApps.length === 0 ? (
            // Empty State
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-8">
                <div className="w-24 h-24 bg-macos-surface rounded-3xl flex items-center justify-center mb-6 mx-auto shadow-lg border border-macos-border">
                  <img
                    src="/duck-icon.png"
                    alt="FileDuck Logo"
                    className="w-16 h-16"
                  />
                </div>
                <h3 className={`${Typography.title2} mb-3`}>
                  No Apps Installed
                </h3>
                <p className={`${Typography.bodySecondary} text-lg mb-8 max-w-md`}>
                  Get started by installing your first tool from the Tool Store.
                  Discover powerful utilities to enhance your workflow.
                </p>
                <MacOSButton
                  onClick={() => navigate('/store')}
                  variant="primary"
                  size="lg"
                  className="shadow-lg hover:shadow-xl"
                >
                  Install Your First App
                </MacOSButton>
              </div>
            </div>
          ) : (
            // apps List - Apple App Store Style (2-column grid)
            <div className="grid grid-cols-2 gap-x-8">
              <div className="space-y-0">
                {installedApps.filter((_, index) => index % 2 === 0).map((app, index, filteredApps) => (
                  <AppCard
                    key={app.id}
                    app={app}
                    showDivider={index < filteredApps.length - 1}
                    onOpen={handleAppOpen}
                    onUninstall={handleAppUninstall}
                    onDetails={handleAppDetails}
                  />
                ))}
              </div>
              <div className="space-y-0">
                {installedApps.filter((_, index) => index % 2 === 1).map((app, index, filteredApps) => (
                  <AppCard
                    key={app.id}
                    app={app}
                    showDivider={index < filteredApps.length - 1}
                    onOpen={handleAppOpen}
                    onUninstall={handleAppUninstall}
                    onDetails={handleAppDetails}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* App Details Dialog */}
      <AppDetailsDialog
        app={selectedApp}
        isOpen={showAppDetails}
        onClose={() => {
          setShowAppDetails(false);
          setSelectedApp(null);
        }}
        onUninstall={handleAppUninstall}
      />

      {/* Uninstall Confirmation Dialog */}
      <UninstallConfirmDialog
        app={appToUninstall}
        isOpen={showUninstallConfirm}
        onClose={() => {
          setShowUninstallConfirm(false);
          setAppToUninstall(null);
        }}
        onConfirm={handleConfirmUninstall}
      />
    </PageWrapper>
  );
};

export default HomeView;
