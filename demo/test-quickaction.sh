#!/bin/bash

# FileDuck QuickAction Demo Script
# Creates test files and demonstrates QuickAction functionality

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🚀 FileDuck QuickAction Demo${NC}"
echo -e "${BLUE}=============================${NC}"
echo ""

# Get project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DEMO_DIR="$PROJECT_DIR/demo"
TEST_FILES_DIR="$DEMO_DIR/test-files"

# Create demo directory structure
mkdir -p "$TEST_FILES_DIR"

echo -e "${BLUE}📁 Creating test files...${NC}"

# Create test CSV file
cat > "$TEST_FILES_DIR/sample.csv" << 'EOF'
name,age,city,occupation
<PERSON>,28,<PERSON>,Software Engineer
<PERSON>,32,<PERSON>,Product Manager
<PERSON>,45,Chicago,Data Scientist
<PERSON>,29,Seattle,<PERSON><PERSON> Designer
<PERSON>,35,Austin,DevOps Engineer
EOF

# Create test JSON file
cat > "$TEST_FILES_DIR/sample.json" << 'EOF'
{
  "users": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "age": 28
    },
    {
      "id": 2,
      "name": "Jane <PERSON>",
      "email": "<EMAIL>",
      "age": 32
    }
  ],
  "metadata": {
    "version": "1.0",
    "created": "2024-01-15"
  }
}
EOF

# Create test text file
cat > "$TEST_FILES_DIR/sample.txt" << 'EOF'
This is a sample text file for testing FileDuck QuickAction functionality.

It contains multiple lines of text that can be processed by various FileDuck apps.

Features to test:
- File type detection
- App compatibility matching
- QuickAction dialog display
- File processing workflow

Generated by FileDuck QuickAction Demo Script.
EOF

echo -e "${GREEN}✅ Created test files:${NC}"
echo -e "   📄 $TEST_FILES_DIR/sample.csv"
echo -e "   📄 $TEST_FILES_DIR/sample.json"
echo -e "   📄 $TEST_FILES_DIR/sample.txt"
echo ""

echo -e "${BLUE}🔧 Testing QuickAction functionality...${NC}"

# Test 1: Check if QuickAction workflow is installed
echo -e "${YELLOW}Test 1: Checking QuickAction workflow installation${NC}"
WORKFLOW_PATH="$HOME/Library/Services/FileDuck Dev Convert.workflow"
if [ -d "$WORKFLOW_PATH" ]; then
    echo -e "${GREEN}✅ QuickAction workflow is installed${NC}"
else
    echo -e "${YELLOW}⚠️  QuickAction workflow not found. Run: ./scripts/install-dev-quickaction.sh${NC}"
fi
echo ""

# Test 2: Test CLI argument processing
echo -e "${YELLOW}Test 2: Testing CLI argument processing${NC}"
echo -e "${BLUE}Starting FileDuck with test CSV file...${NC}"
echo -e "${BLUE}Command: npm start -- \"$TEST_FILES_DIR/sample.csv\"${NC}"
echo ""

# Ask user if they want to run the test
read -p "$(echo -e ${BLUE}"Would you like to start FileDuck with the test CSV file? (y/N): "${NC})" -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    cd "$PROJECT_DIR"
    echo -e "${GREEN}🚀 Starting FileDuck with QuickAction test file...${NC}"
    npm start -- "$TEST_FILES_DIR/sample.csv"
else
    echo -e "${BLUE}💡 Manual testing instructions:${NC}"
    echo ""
    echo -e "${YELLOW}To test QuickAction manually:${NC}"
    echo -e "1. Install the workflow: ${BLUE}./scripts/install-dev-quickaction.sh${NC}"
    echo -e "2. Open Finder and navigate to: ${BLUE}$TEST_FILES_DIR${NC}"
    echo -e "3. Right-click on any test file"
    echo -e "4. Select 'Quick Actions' → 'FileDuck Dev Convert'"
    echo -e "5. FileDuck should launch with the QuickAction dialog"
    echo ""
    echo -e "${YELLOW}To test CLI directly:${NC}"
    echo -e "   ${BLUE}cd $PROJECT_DIR${NC}"
    echo -e "   ${BLUE}npm start -- \"$TEST_FILES_DIR/sample.csv\"${NC}"
    echo ""
    echo -e "${YELLOW}To test multiple files:${NC}"
    echo -e "   ${BLUE}npm start -- \"$TEST_FILES_DIR/sample.csv\" \"$TEST_FILES_DIR/sample.json\"${NC}"
    echo ""
fi

echo -e "${GREEN}✨ Demo setup complete!${NC}"
echo ""
echo -e "${BLUE}📋 Test files created in: ${NC}$TEST_FILES_DIR"
echo -e "${BLUE}🔍 Check logs at: ${NC}/tmp/fileduck-quickaction.log"
echo ""
