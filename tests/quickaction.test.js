const { test, expect } = require('@playwright/test');
const path = require('path');
const fs = require('fs');

/**
 * QuickAction Integration Tests
 * Tests the right-click context menu integration functionality
 */

test.describe('QuickAction Integration', () => {
  let electronApp;
  let page;

  test.beforeAll(async ({ playwright }) => {
    // Launch Electron app
    electronApp = await playwright._electron.launch({
      args: ['.'],
      env: {
        NODE_ENV: 'test'
      }
    });
    
    page = await electronApp.firstWindow();
    await page.waitForLoadState('domcontentloaded');
  });

  test.afterAll(async () => {
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('should handle CLI arguments for QuickAction', async () => {
    // Create a test CSV file
    const testDir = path.join(__dirname, '../temp');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    const testFile = path.join(testDir, 'test.csv');
    fs.writeFileSync(testFile, 'name,age,city\nJohn,25,NYC\nJane,30,LA');

    // Test that the app can process QuickAction files
    const result = await page.evaluate(async (filePath) => {
      if (window.electronAPI && window.electronAPI.processQuickActionFiles) {
        return await window.electronAPI.processQuickActionFiles([filePath]);
      }
      return null;
    }, testFile);

    expect(result).toBeTruthy();
    expect(result.success).toBe(true);
    expect(result.files).toHaveLength(1);
    expect(result.files[0].extension).toBe('.csv');

    // Cleanup
    fs.unlinkSync(testFile);
  });

  test('should show QuickAction dialog when files are processed', async () => {
    // Create test files
    const testDir = path.join(__dirname, '../temp');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    const testFile = path.join(testDir, 'test.csv');
    fs.writeFileSync(testFile, 'name,age\nTest,25');

    // Trigger QuickAction
    await page.evaluate(async (filePath) => {
      if (window.electronAPI && window.electronAPI.processQuickActionFiles) {
        const result = await window.electronAPI.processQuickActionFiles([filePath]);
        // Simulate the event that would normally come from main process
        if (window.dispatchEvent) {
          window.dispatchEvent(new CustomEvent('quickaction-files', { 
            detail: [filePath] 
          }));
        }
      }
    }, testFile);

    // Wait for dialog to appear
    await page.waitForTimeout(1000);

    // Check if QuickAction dialog elements exist
    const dialogExists = await page.evaluate(() => {
      return document.querySelector('[data-testid="quickaction-dialog"]') !== null ||
             document.querySelector('.quickaction-dialog') !== null ||
             document.textContent.includes('FileDuck QuickAction');
    });

    // Note: This test might need adjustment based on actual dialog implementation
    console.log('Dialog check result:', dialogExists);

    // Cleanup
    fs.unlinkSync(testFile);
  });

  test('should find compatible apps for CSV files', async () => {
    const result = await page.evaluate(async () => {
      if (window.electronAPI && window.electronAPI.getAppsForExtension) {
        return await window.electronAPI.getAppsForExtension('.csv');
      }
      return [];
    });

    expect(Array.isArray(result)).toBe(true);
    // Should include csv-to-pdf if it's installed
    console.log('Compatible apps for .csv:', result);
  });

  test('should get supported file extensions', async () => {
    const extensions = await page.evaluate(async () => {
      if (window.electronAPI && window.electronAPI.getSupportedExtensions) {
        return await window.electronAPI.getSupportedExtensions();
      }
      return [];
    });

    expect(Array.isArray(extensions)).toBe(true);
    expect(extensions.length).toBeGreaterThan(0);
    expect(extensions).toContain('.csv');
    console.log('Supported extensions:', extensions);
  });

  test('should register file type support for apps', async () => {
    const result = await page.evaluate(async () => {
      if (window.electronAPI && window.electronAPI.registerFileTypeSupport) {
        return await window.electronAPI.registerFileTypeSupport('test-app', ['.txt', '.md']);
      }
      return false;
    });

    expect(result).toBe(true);
  });

  test('workflow installation script should exist', () => {
    const scriptPath = path.join(__dirname, '../scripts/install-dev-quickaction.sh');
    expect(fs.existsSync(scriptPath)).toBe(true);
    
    const scriptContent = fs.readFileSync(scriptPath, 'utf8');
    expect(scriptContent).toContain('FileDuck Dev QuickAction');
    expect(scriptContent).toContain('npm start');
  });

  test('workflow files should exist', () => {
    const workflowPath = path.join(__dirname, '../resources/FileDuck Dev Convert.workflow');
    expect(fs.existsSync(workflowPath)).toBe(true);
    
    const plistPath = path.join(workflowPath, 'Contents/Info.plist');
    expect(fs.existsSync(plistPath)).toBe(true);
    
    const plistContent = fs.readFileSync(plistPath, 'utf8');
    expect(plistContent).toContain('FileDuck Dev Convert');
    expect(plistContent).toContain('npm start');
  });
});

test.describe('QuickAction Service Unit Tests', () => {
  test('should validate file extensions correctly', () => {
    const { isValidFileExtension, normalizeFileExtension } = require('../src/schemas/quickActionSchema');
    
    expect(isValidFileExtension('.csv')).toBe(true);
    expect(isValidFileExtension('csv')).toBe(true);
    expect(isValidFileExtension('.PDF')).toBe(true);
    expect(isValidFileExtension('')).toBe(false);
    expect(isValidFileExtension('.')).toBe(false);
    
    expect(normalizeFileExtension('CSV')).toBe('.csv');
    expect(normalizeFileExtension('.PDF')).toBe('.pdf');
    expect(normalizeFileExtension('txt')).toBe('.txt');
  });

  test('should validate QuickAction request schema', () => {
    const { validateQuickActionRequest } = require('../src/schemas/quickActionSchema');
    
    const validRequest = {
      filePaths: ['/path/to/file.csv'],
      source: 'cli'
    };
    
    const result = validateQuickActionRequest(validRequest);
    expect(result.success).toBe(true);
    expect(result.data.filePaths).toEqual(['/path/to/file.csv']);
    
    const invalidRequest = {
      filePaths: [],
      source: 'invalid'
    };
    
    const invalidResult = validateQuickActionRequest(invalidRequest);
    expect(invalidResult.success).toBe(false);
  });
});
