#!/bin/bash

# FileDuck Dev QuickAction Installation Script
# Installs the macOS Quick Action for right-click context menu integration

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Emojis for Gen-Z vibes
FIRE="🔥"
ROCKET="🚀"
CHECK="✅"
WARN="⚠️"
ERROR="❌"

echo -e "${BLUE}${ROCKET} FileDuck Dev QuickAction Installer${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Get the absolute path to the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
WORKFLOW_NAME="FileDuck Dev Convert.workflow"
WORKFLOW_SOURCE="$PROJECT_DIR/resources/$WORKFLOW_NAME"
WORKFLOW_TARGET="$HOME/Library/Services/$WORKFLOW_NAME"

echo -e "${BLUE}Project Directory: ${NC}$PROJECT_DIR"
echo -e "${BLUE}Workflow Source: ${NC}$WORKFLOW_SOURCE"
echo -e "${BLUE}Workflow Target: ${NC}$WORKFLOW_TARGET"
echo ""

# Check if source workflow exists
if [ ! -d "$WORKFLOW_SOURCE" ]; then
    echo -e "${ERROR} ${RED}Workflow source not found: $WORKFLOW_SOURCE${NC}"
    echo -e "${YELLOW}Make sure you're running this script from the FileDuck project root${NC}"
    exit 1
fi

# Create Services directory if it doesn't exist
echo -e "${BLUE}${FIRE} Creating Services directory...${NC}"
mkdir -p "$HOME/Library/Services"

# Remove existing workflow if it exists
if [ -d "$WORKFLOW_TARGET" ]; then
    echo -e "${YELLOW}${WARN} Removing existing workflow...${NC}"
    rm -rf "$WORKFLOW_TARGET"
fi

# Copy the workflow
echo -e "${BLUE}${FIRE} Installing QuickAction workflow...${NC}"
cp -R "$WORKFLOW_SOURCE" "$WORKFLOW_TARGET"

# Update the project path in the workflow
echo -e "${BLUE}${FIRE} Updating project path in workflow...${NC}"
PLIST_FILE="$WORKFLOW_TARGET/Contents/Info.plist"
WFLOW_FILE="$WORKFLOW_TARGET/Contents/document.wflow"

# Use sed to replace the placeholder with the actual project directory in both files
if sed -i '' "s|PLACEHOLDER_PROJECT_DIR|$PROJECT_DIR|g" "$PLIST_FILE"; then
    echo -e "${CHECK} ${GREEN}Updated project path in Info.plist${NC}"
else
    echo -e "${WARN} ${YELLOW}Warning: Could not update project path in Info.plist${NC}"
fi

if sed -i '' "s|PLACEHOLDER_PROJECT_DIR|$PROJECT_DIR|g" "$WFLOW_FILE"; then
    echo -e "${CHECK} ${GREEN}Updated project path in document.wflow${NC}"
else
    echo -e "${WARN} ${YELLOW}Warning: Could not update project path in document.wflow${NC}"
fi

# Set proper permissions
chmod -R 755 "$WORKFLOW_TARGET"

echo ""
echo -e "${CHECK} ${GREEN}QuickAction installed successfully!${NC}"
echo ""
echo -e "${BLUE}${FIRE} How to use:${NC}"
echo -e "1. Right-click on any file in Finder"
echo -e "2. Look for 'Quick Actions' or 'Services' in the context menu"
echo -e "3. Select 'FileDuck Dev Convert'"
echo -e "4. FileDuck will launch with the selected file(s)"
echo ""
echo -e "${YELLOW}${WARN} Note: macOS may cache Quick Actions. If you don't see the option:${NC}"
echo -e "- Log out and log back in, or"
echo -e "- Restart Finder: ${BLUE}killall Finder${NC}"
echo ""
echo -e "${BLUE}${FIRE} Debug logs are written to: ${NC}/tmp/fileduck-quickaction.log"
echo ""

# Ask if user wants to test the installation
read -p "$(echo -e ${BLUE}${ROCKET} "Would you like to test the installation by starting FileDuck now? (y/N): "${NC})" -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}${FIRE} Starting FileDuck...${NC}"
    cd "$PROJECT_DIR"
    npm start
else
    echo -e "${CHECK} ${GREEN}Installation complete! Test it by right-clicking a file in Finder.${NC}"
fi
