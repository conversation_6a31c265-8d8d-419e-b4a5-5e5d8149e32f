#!/bin/bash

# FileDuck Dev QuickAction Installation Script
# Installs the macOS Quick Action for right-click context menu integration

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Emojis for Gen-Z vibes
FIRE="🔥"
ROCKET="🚀"
CHECK="✅"
WARN="⚠️"
ERROR="❌"

echo -e "${BLUE}${ROCKET} FileDuck Dev QuickAction Installer${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Get the absolute path to the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
WORKFLOW_NAME="FileDuck Dev Convert.workflow"
WORKFLOW_SOURCE="$PROJECT_DIR/resources/$WORKFLOW_NAME"
WORKFLOW_TARGET="$HOME/Library/Services/$WORKFLOW_NAME"

echo -e "${BLUE}Project Directory: ${NC}$PROJECT_DIR"
echo -e "${BLUE}Workflow Source: ${NC}$WORKFLOW_SOURCE"
echo -e "${BLUE}Workflow Target: ${NC}$WORKFLOW_TARGET"
echo ""

# Check if source workflow exists
if [ ! -d "$WORKFLOW_SOURCE" ]; then
    echo -e "${ERROR} ${RED}Workflow source not found: $WORKFLOW_SOURCE${NC}"
    echo -e "${YELLOW}Make sure you're running this script from the FileDuck project root${NC}"
    exit 1
fi

# Create Services directory if it doesn't exist
echo -e "${BLUE}${FIRE} Creating Services directory...${NC}"
mkdir -p "$HOME/Library/Services"

# Remove existing workflow if it exists
if [ -d "$WORKFLOW_TARGET" ]; then
    echo -e "${YELLOW}${WARN} Removing existing workflow...${NC}"
    rm -rf "$WORKFLOW_TARGET"
fi

# Copy the workflow
echo -e "${BLUE}${FIRE} Installing QuickAction workflow...${NC}"
cp -R "$WORKFLOW_SOURCE" "$WORKFLOW_TARGET"

# Update the project path in the workflow
echo -e "${BLUE}${FIRE} Updating project path in workflow...${NC}"
PLIST_FILE="$WORKFLOW_TARGET/Contents/Info.plist"

# Create a temporary script to replace the PROJECT_DIR placeholder
TEMP_SCRIPT=$(mktemp)
cat > "$TEMP_SCRIPT" << 'EOF'
#!/bin/bash

# FileDuck Dev QuickAction Script
# This script launches FileDuck in development mode with selected files

PROJECT_DIR="PROJECT_DIR_PLACEHOLDER"
cd "$PROJECT_DIR"

# Log the action for debugging
echo "FileDuck QuickAction triggered at $(date)" >> /tmp/fileduck-quickaction.log
echo "Files: $@" >> /tmp/fileduck-quickaction.log
echo "Project Dir: $PROJECT_DIR" >> /tmp/fileduck-quickaction.log

# Check if npm start is already running
if pgrep -f "npm.*start" > /dev/null; then
    echo "FileDuck is already running, focusing window..." >> /tmp/fileduck-quickaction.log
    # Just pass the files to the running instance via a temp file
    echo "$@" > /tmp/fileduck-quickaction-files.txt
    osascript -e 'tell application "FileDuck" to activate' 2>/dev/null || true
else
    echo "Starting FileDuck with files..." >> /tmp/fileduck-quickaction.log
    # Start FileDuck with the selected files as arguments
    npm start -- "$@" &
fi
EOF

# Replace placeholder with actual project directory
sed "s|PROJECT_DIR_PLACEHOLDER|$PROJECT_DIR|g" "$TEMP_SCRIPT" > "${TEMP_SCRIPT}.final"

# Escape the script content for XML
ESCAPED_SCRIPT=$(sed 's/&/\&amp;/g; s/</\&lt;/g; s/>/\&gt;/g; s/"/\&quot;/g; s/'"'"'/\&#39;/g' "${TEMP_SCRIPT}.final")

# Update the plist file with the correct script
if command -v plutil >/dev/null 2>&1; then
    # Use plutil if available (more reliable)
    plutil -replace actions.0.ActionParameters.COMMAND_STRING -string "$(<"${TEMP_SCRIPT}.final")" "$PLIST_FILE"
    echo -e "${CHECK} ${GREEN}Updated workflow script using plutil${NC}"
else
    # Fallback to sed (less reliable but works)
    echo -e "${YELLOW}${WARN} plutil not available, using sed fallback${NC}"
    # This is a simplified approach - in production, you'd want a more robust XML parser
    echo -e "${YELLOW}Manual update may be required for the workflow script${NC}"
fi

# Clean up temp files
rm -f "$TEMP_SCRIPT" "${TEMP_SCRIPT}.final"

# Set proper permissions
chmod -R 755 "$WORKFLOW_TARGET"

echo ""
echo -e "${CHECK} ${GREEN}QuickAction installed successfully!${NC}"
echo ""
echo -e "${BLUE}${FIRE} How to use:${NC}"
echo -e "1. Right-click on any file in Finder"
echo -e "2. Look for 'Quick Actions' or 'Services' in the context menu"
echo -e "3. Select 'FileDuck Dev Convert'"
echo -e "4. FileDuck will launch with the selected file(s)"
echo ""
echo -e "${YELLOW}${WARN} Note: macOS may cache Quick Actions. If you don't see the option:${NC}"
echo -e "- Log out and log back in, or"
echo -e "- Restart Finder: ${BLUE}killall Finder${NC}"
echo ""
echo -e "${BLUE}${FIRE} Debug logs are written to: ${NC}/tmp/fileduck-quickaction.log"
echo ""

# Ask if user wants to test the installation
read -p "$(echo -e ${BLUE}${ROCKET} "Would you like to test the installation by starting FileDuck now? (y/N): "${NC})" -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}${FIRE} Starting FileDuck...${NC}"
    cd "$PROJECT_DIR"
    npm start
else
    echo -e "${CHECK} ${GREEN}Installation complete! Test it by right-clicking a file in Finder.${NC}"
fi
