# 🐍 FileDuck Python Utility Runtime: Integration Prompt for Cursor (Spawn-Based)

## 🎯 Purpose
This prompt tells Cursor to refactor our **existing desktop app** (already built and working) to support a new type of utility: **Python-powered, locally executed scripts**, using `main.py` and `config.json`.  
We’ll use a **spawn-based architecture** for maximum safety and simplicity.

---

## ✅ Final Prompt (Copy-Paste into Cursor)

**Prompt Title:**  
Enable support for Python-based utilities in the current desktop app (spawn-based)

**Prompt Content:**  

We already have a fully working desktop app. You don’t need to build it — your task is to add **Python utility execution support using `child_process.spawn()`**.

---

### ✅ NEW UTILITY FORMAT

All utilities will now include:

```
/dist/
├── main.py         # Python logic
└── config.json     # Utility metadata & pip dependencies
```

### ✅ `config.json` Format

```json
{
  "name": "json-cleaner",
  "version": "1.0.0",
  "entry": "main.py",
  "requirements": [
    "pandas",
    "numpy"
  ],
  "ui": {
    "title": "Clean JSON to CSV",
    "description": "Load a JSON and export it as CSV",
    "inputType": "file"
  }
}
```

---

## 🚀 IMPLEMENTATION: SPAWN-BASED PYTHON RUNNER

### 1. Install Utility

- On `.zip` install:
  - Unzip to: `/userData/utilities/{utility-name}/`
  - Read `config.json`
  - Run: `pip3 install {dependency}` for each package in `requirements` (skip if already installed)

### 2. Run Utility

Use `child_process.spawn()` in Node.js:

```js
const { spawn } = require("child_process");

function runPythonUtility(scriptPath, inputData, callback) {
  const python = spawn("python3", [scriptPath]);

  let output = "";
  let error = "";

  python.stdin.write(JSON.stringify(inputData));
  python.stdin.end();

  python.stdout.on("data", data => output += data.toString());
  python.stderr.on("data", data => error += data.toString());

  python.on("close", code => {
    if (code === 0) callback(null, output);
    else callback(error, null);
  });
}
```

### 3. Sample Python Utility (`main.py`)

```python
import sys, json, pandas as pd

data = json.load(sys.stdin)
df = pd.read_json(data["inputPath"])
df.to_csv(data["inputPath"] + ".csv", index=False)
print("✅ Done")
```

---

### ⚠️ DO NOT:

- Don’t reimplement the app — it’s done.
- Don’t use Pyodide or WASM for Python.
- Don’t run pip install on every run.

---

### ✅ Final Outcome

When I drop a zip with a Python utility:
- It installs via the app
- Installs dependencies once
- Runs `main.py` using spawn
- Returns stdout to the UI

---

Let me know when ready for test zip or packaging script.