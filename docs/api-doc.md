# FileDuck API Documentation

## Base URL
```
http://localhost:8000
```

## Authentication
- **Header**: `user-id: {user_id}` (required for POST endpoints)
- **Rate Limit**: 20 requests per minute per user

---

## Playstore Apps API (`/api/playstore/apps/v1`)

### 1. Get All Apps
- **GET** `/api/playstore/apps/v1`
- **Description**: Get all applications with pagination
- **Query Parameters**:
  - `skip` (int): Number of apps to skip (default: 0)
  - `limit` (int): Number of apps to return (default: 10, max: 100)
- **Response**: Array of App objects

### 2. Get App Categories
- **GET** `/api/playstore/apps/v1/categories`
- **Description**: Get all app categories with counts
- **Response**: Array of `{category: string, count: number}`

### 3. Get Apps by Category
- **GET** `/api/playstore/apps/v1/category/{category}`
- **Description**: Get apps in specific category
- **Parameters**: `category` (string): Category name
- **Query Parameters**: `skip`, `limit`
- **Response**: Array of App objects

### 4. Get App by ID
- **GET** `/api/playstore/apps/v1/{app_id}`
- **Description**: Get specific app details
- **Parameters**: `app_id` (string): Application ID
- **Response**: App object

### 5. Get Popular Apps
- **GET** `/api/playstore/apps/v1/popular`
- **Description**: Get popular apps by install count
- **Query Parameters**: `skip`, `limit`
- **Response**: Array of App objects

### 6. Get Trending Apps
- **GET** `/api/playstore/apps/v1/trending`
- **Description**: Get trending apps (last 7 days)
- **Query Parameters**: `skip`, `limit`
- **Response**: Array of App objects

### 7. Get New Apps
- **GET** `/api/playstore/apps/v1/new`
- **Description**: Get new apps (last 7 days)
- **Query Parameters**: `skip`, `limit`
- **Response**: Array of App objects

### 8. Get Recommended Apps
- **GET** `/api/playstore/apps/v1/recommendation`
- **Description**: Get recommended apps for user
- **Query Parameters**: `skip`, `limit`
- **Response**: Array of App objects

### 9. Search Apps
- **GET** `/api/playstore/apps/v1/search?q={query}`
- **Description**: Search apps by name, description, tags, category
- **Query Parameters**:
  - `q` (string, required): Search query
  - `skip`, `limit`
- **Response**: Array of App objects

### 10. Install App
- **POST** `/api/playstore/apps/v1/{app_id}/install`
- **Description**: Record app installation (increments install count)
- **Headers**: `user-id: {user_id}`
- **Response**: `{message: string, app_id: string}`

### 11. Uninstall App
- **POST** `/api/playstore/apps/v1/{app_id}/uninstall`
- **Description**: Record app uninstallation (increments uninstall count)
- **Headers**: `user-id: {user_id}`
- **Response**: `{message: string, app_id: string}`

### 12. Run App
- **POST** `/api/playstore/apps/v1/{app_id}/run`
- **Description**: Record app run (increments run count)
- **Headers**: `user-id: {user_id}`
- **Response**: `{message: string, app_id: string}`

### 13. Rate App
- **POST** `/api/playstore/apps/v1/{app_id}/rate?rating={1-5}`
- **Description**: Rate an app (1-5 stars)
- **Headers**: `user-id: {user_id}`
- **Query Parameters**: `rating` (int): Rating from 1 to 5
- **Response**: `{message: string, app_id: string, rating: number}`

### 14. Add Review
- **POST** `/api/playstore/apps/v1/{app_id}/review`
- **Description**: Add a review for an app
- **Headers**: `user-id: {user_id}`, `Content-Type: application/json`
- **Body**: `{user_id: string, rating: number, review_text?: string}`
- **Response**: Review object

### 15. Get Download URL
- **GET** `/api/playstore/apps/v1/{app_id}/download`
- **Description**: Get app download URL
- **Headers**: `user-id: {user_id}`
- **Response**: `{app_id: string, download_url: string, version: string, name: string}`

---

## Data Models

### App Model
```json
{
  "_id": "string",
  "app_id": "string",
  "name": "string",
  "description": "string",
  "source": "string",
  "destination": "string",
  "version": "string",
  "releases": [],
  "install": 0,
  "uninstall": 0,
  "successful_run": 0,
  "failed_run": 0,
  "run_count": 0,
  "installer_url": "string",
  "category": "string",
  "sub_category": "string",
  "search_tags": ["string"],
  "rating_count": 0,
  "rating_sum": 0,
  "average_rating": 0.0,
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Review Model
```json
{
  "_id": "string",
  "app_id": "string",
  "user_id": "string",
  "rating": 5,
  "review_text": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Category Response
```json
{
  "category": "string",
  "count": 0
}
```

---

## Example Usage

### Get Categories
```bash
curl -X GET "http://localhost:8000/api/playstore/apps/v1/categories"
```

### Search Apps
```bash
curl -X GET "http://localhost:8000/api/playstore/apps/v1/search?q=pdf&limit=5"
```

### Install App
```bash
curl -X POST "http://localhost:8000/api/playstore/apps/v1/pdf-converter-1/install" \
  -H "user-id: user123"
```

### Rate App
```bash
curl -X POST "http://localhost:8000/api/playstore/apps/v1/pdf-converter-1/rate?rating=5" \
  -H "user-id: user123"
```

### Add Review
```bash
curl -X POST "http://localhost:8000/api/playstore/apps/v1/pdf-converter-1/review" \
  -H "user-id: user123" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "rating": 5, "review_text": "Great app!"}'
```

---

## Error Responses

All endpoints return standard HTTP status codes:
- `200`: Success
- `400`: Bad Request (validation error)
- `401`: Unauthorized (missing user-id header)
- `404`: Not Found
- `429`: Rate Limit Exceeded
- `500`: Internal Server Error

Error format:
```json
{
  "detail": "Error message"
}
```



---

## Data Models

### App Model
```json
{
  "app_id": "string",
  "name": "string",
  "description": "string",
  "source": "string",
  "destination": "string",
  "version": "string",
  "releases": [],
  "install": 0,
  "uninstall": 0,
  "successful_run": 0,
  "failed_run": 0,
  "installer_url": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### User Model
```json
{
  "user_id": "string",
  "email": "string",
  "name": "string",
  "picture": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Activity Model
```json
{
  "app_id": "string",
  "user_id": "string",
  "type": "string",
  "metadata": {},
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Payment Model
```json
{
  "payment_id": "string",
  "user_id": "string",
  "amount": 0,
  "status": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

---

## Error Responses

All endpoints return standard HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `404`: Not Found
- `500`: Internal Server Error

Error response format:
```json
{
  "detail": "Error message"
}
```

---

## Running the API

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Start MongoDB (make sure it's running on localhost:27017)

3. Run the API:
```bash
python -m app.main
```

4. Access the interactive API documentation:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
