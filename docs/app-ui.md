# Apple-Style Design Guidelines for CSV to PDF Converter

## Core Design Philosophy

### Apple Design Principles
- **Clarity**: Remove all unnecessary visual elements and text
- **Deference**: Content takes precedence over UI
- **Depth**: Use subtle shadows and layering, not heavy backgrounds
- **Minimalism**: Every element must serve a purpose
- **Consistency**: Follow established macOS patterns

### Visual Hierarchy
- Use typography weight and spacing, not colors or backgrounds
- Maintain consistent 8px grid system
- Embrace whitespace as a design element

## Component 1: DropZone Component

### Layout Structure
```
[Drop Zone Area] [Convert Button] [Converted Files Area]
     (40%)           (20%)              (40%)
```

### Specifications

#### Container
- **Layout**: Horizontal flexbox with equal spacing
- **Padding**: 24px all sides
- **Background**: Pure white (#FFFFFF)
- **Border**: None
- **Max-width**: 1200px
- **Gap**: 32px between sections

#### Left: Drop Zone Area
- **Dimensions**: Flexible width, 200px min-height
- **Border**: 2px dashed #D1D5DB (only when empty)
- **Border-radius**: 8px
- **Background**: #F9FAFB (subtle gray, only when dragging)
- **Hover state**: Border color changes to #3B82F6
- **Active state**: Background becomes #EFF6FF
- **Typography**:
    - Primary text: SF Pro Display, 16px, Medium, #1F2937
    - Secondary text: SF Pro Text, 13px, Regular, #6B7280
- **Icon**: Upload icon (24px, #6B7280)
- **Padding**: 32px

#### Selected Files List
- **Background**: None
- **List items**:
    - Padding: 8px 0
    - Border-bottom: 1px solid #F3F4F6 (except last item)
    - Typography: SF Pro Text, 13px, Regular, #374151
    - File size: SF Pro Text, 11px, Regular, #9CA3AF
- **Remove button**: X icon, 14px, #9CA3AF, hover #EF4444

#### Center: Convert Button
- **Style**: Primary macOS button
- **Background**: #007AFF (Apple blue)
- **Hover**: #0056CC
- **Active**: #004299
- **Typography**: SF Pro Text, 13px, Medium, White
- **Padding**: 8px 16px
- **Border-radius**: 6px
- **Height**: 32px
- **Shadow**: 0 1px 2px rgba(0, 0, 0, 0.05)
- **Disabled state**: #E5E7EB background, #9CA3AF text

#### Right: Converted Files Area
- **Background**: #F9FAFB
- **Border**: 1px solid #E5E7EB
- **Border-radius**: 8px
- **Padding**: 16px
- **Empty state**:
    - Icon: Checkmark circle, 32px, #10B981
    - Text: "Converted files will appear here"
    - Typography: SF Pro Text, 13px, Regular, #6B7280

#### File Items (Converted)
- **Layout**: List with file icons
- **Clickable**: Entire row clickable to open in Finder
- **Hover**: Background #F3F4F6
- **Typography**: SF Pro Text, 13px, Regular, #374151
- **Icon**: PDF icon, 16px
- **Spacing**: 8px between items

### Error States
- **Background**: #FEF2F2
- **Border**: 1px solid #FECACA
- **Text color**: #DC2626
- **Typography**: SF Pro Text, 13px, Regular

## Component 2: Settings Component

### Container
- **Background**: Pure white (#FFFFFF)
- **Padding**: 24px
- **Border**: None
- **Max-width**: 800px

### Grid System
- **Layout**: CSS Grid
- **Columns**: Auto-fit with minimum 200px
- **Gap**: 16px vertical, 24px horizontal
- **Responsive**: Single column on mobile

### Form Row Structure
```
Row 1: [Dropdown] [Dropdown]
Row 2: [Slider] [Input] [Switch]
Row 3: [Custom configuration...]
```

### Form Elements Specifications

#### Labels
- **Typography**: SF Pro Text, 13px, Medium, #374151
- **Margin-bottom**: 6px
- **Required asterisk**: #DC2626

#### Dropdowns
- **Background**: White
- **Border**: 1px solid #D1D5DB
- **Border-radius**: 6px
- **Padding**: 8px 12px
- **Typography**: SF Pro Text, 13px, Regular, #374151
- **Height**: 32px
- **Focus**: Border color #007AFF, shadow 0 0 0 3px rgba(0, 122, 255, 0.1)
- **Arrow**: Chevron down, 12px, #6B7280

#### Sliders
- **Track**: 4px height, #E5E7EB background
- **Fill**: #007AFF
- **Thumb**: 16px circle, white, 2px border #007AFF
- **Shadow**: 0 1px 3px rgba(0, 0, 0, 0.1)

#### Input Fields
- **Background**: White
- **Border**: 1px solid #D1D5DB
- **Border-radius**: 6px
- **Padding**: 8px 12px
- **Typography**: SF Pro Text, 13px, Regular, #374151
- **Height**: 32px
- **Focus**: Same as dropdowns
- **Placeholder**: #9CA3AF

#### Switches
- **Track**: 28px width, 16px height, #E5E7EB background
- **Active track**: #007AFF
- **Thumb**: 12px circle, white, smooth transition
- **Shadow**: 0 1px 2px rgba(0, 0, 0, 0.05)

### Section Grouping
- **Spacing**: 32px between logical groups
- **Separator**: 1px solid #F3F4F6 (optional, only when needed)
- **Group titles**:
    - Typography: SF Pro Display, 15px, Medium, #1F2937
    - Margin: 16px bottom, 24px top (except first)

## Configuration Props

### DropZone Component Props
```typescript
interface DropZoneProps {
  maxFiles?: number;
  acceptedFileTypes?: string[];
  onFilesAdded?: (files: File[]) => void;
  onConvert?: () => void;
  onFileRemove?: (index: number) => void;
  onConvertedFileClick?: (file: ConvertedFile) => void;
  isConverting?: boolean;
  convertedFiles?: ConvertedFile[];
  selectedFiles?: File[];
  errorMessage?: string;
}
```

### Settings Component Props
```typescript
interface SettingsProps {
  sections: SettingSection[];
  onSettingChange?: (key: string, value: any) => void;
  values?: Record<string, any>;
}

interface SettingSection {
  title?: string;
  rows: SettingRow[];
}

interface SettingRow {
  items: SettingItem[];
}

interface SettingItem {
  type: 'dropdown' | 'slider' | 'input' | 'switch';
  key: string;
  label: string;
  required?: boolean;
  // Type-specific props
  options?: { label: string; value: any }[]; // dropdown
  min?: number; max?: number; step?: number; // slider
  placeholder?: string; // input
  // ... other specific props
}
```

## Typography System
- **Primary Font**: SF Pro Display (headings)
- **Secondary Font**: SF Pro Text (body, UI)
- **Weights**: Regular (400), Medium (500), Semibold (600)
- **Line Heights**: 1.4 for body text, 1.2 for headings

## Color Palette
- **Primary**: #007AFF (Apple Blue)
- **Success**: #10B981
- **Warning**: #F59E0B
- **Error**: #DC2626
- **Gray-50**: #F9FAFB
- **Gray-100**: #F3F4F6
- **Gray-200**: #E5E7EB
- **Gray-300**: #D1D5DB
- **Gray-400**: #9CA3AF
- **Gray-500**: #6B7280
- **Gray-600**: #4B5563
- **Gray-700**: #374151
- **Gray-800**: #1F2937

## Animation Guidelines
- **Duration**: 200ms for micro-interactions, 300ms for layout changes
- **Easing**: ease-out for entrances, ease-in for exits
- **Properties**: transform and opacity preferred over layout properties
- **Hover states**: 150ms transition
- **Focus states**: Instant (0ms)

## Accessibility Requirements
- **Contrast**: Minimum 4.5:1 for normal text
- **Focus indicators**: Visible and consistent
- **Keyboard navigation**: Full support
- **Screen readers**: Proper ARIA labels
- **Touch targets**: Minimum 44px

## Implementation Notes
- Use CSS Grid and Flexbox for layouts
- Implement proper loading states
- Handle edge cases gracefully
- Ensure responsive design
- Use semantic HTML elements
- Test with macOS system preferences for consistency