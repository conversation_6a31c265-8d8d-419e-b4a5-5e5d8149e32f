# 🚀 FileDuck QuickAction Implementation Guide

## 🎯 Overview

FileDuck QuickAction enables right-click context menu integration on macOS, allowing users to quickly convert files using installed FileDuck apps directly from Finder.

## 🏗️ Architecture

### Core Components

1. **QuickActionService** (`src/main/quickActionService.js`)
   - Handles file type matching and app compatibility
   - Manages file type mappings and app registration
   - Calculates compatibility scores for app ranking

2. **QuickActionDialog** (`src/components/ui/QuickActionDialog.jsx`)
   - Apple-style dialog for app selection
   - Displays compatible apps and file information
   - Handles app execution and user interaction

3. **useQuickAction Hook** (`src/hooks/useQuickAction.js`)
   - React hook for QuickAction state management
   - Handles file processing and app execution
   - Manages dialog state and error handling

4. **macOS Workflow** (`resources/FileDuck Dev Convert.workflow/`)
   - Automator workflow for right-click integration
   - Shell script that launches FileDuck with selected files
   - Handles both new launches and existing instances

## 🔧 Installation

### 1. Install the QuickAction Workflow

```bash
# Run the installation script
./scripts/install-dev-quickaction.sh
```

This script will:
- Copy the workflow to `~/Library/Services/`
- Update the project path in the workflow
- Set proper permissions
- Provide testing instructions

### 2. Restart Finder (if needed)

```bash
# If the QuickAction doesn't appear immediately
killall Finder
```

## 🎮 Usage

### For Users

1. **Right-click any supported file in Finder**
2. **Select "Quick Actions" → "FileDuck Dev Convert"**
3. **FileDuck launches with a dialog showing:**
   - Selected files with metadata
   - Compatible apps ranked by relevance
   - Convert button to execute the selected app

### For Developers

#### Adding File Type Support to Apps

Update your app's `config.json`:

```json
{
  "id": "my-converter",
  "name": "My File Converter",
  "supportedFileTypes": [".csv", ".json"],
  "outputFileTypes": [".pdf", ".xlsx"]
}
```

#### Programmatic Usage

```javascript
// Get supported extensions
const extensions = await window.electronAPI.getSupportedExtensions();

// Find apps for specific extension
const apps = await window.electronAPI.getAppsForExtension('.csv');

// Register file type support
await window.electronAPI.registerFileTypeSupport('my-app', ['.txt', '.md']);

// Process files manually
const result = await window.electronAPI.processQuickActionFiles(['/path/to/file.csv']);
```

## 📁 File Structure

```
FileDuck-OS-Desktop/
├── src/
│   ├── main/
│   │   ├── quickActionService.js      # Core service
│   │   └── main.js                    # Updated with QuickAction integration
│   ├── components/ui/
│   │   └── QuickActionDialog.jsx      # UI dialog component
│   ├── hooks/
│   │   └── useQuickAction.js          # React hook
│   └── schemas/
│       └── quickActionSchema.js       # Zod validation schemas
├── resources/
│   └── FileDuck Dev Convert.workflow/ # macOS Automator workflow
├── scripts/
│   └── install-dev-quickaction.sh     # Installation script
└── tests/
    └── quickaction.test.js            # Test suite
```

## 🔍 File Type Mapping System

### Default Mappings

```javascript
const fileTypeMap = {
  '.csv': ['csv-to-pdf', 'csv-to-json'],
  '.json': ['json-to-csv'],
  '.pdf': ['pdf-to-text'],
  '.png': ['image-converter', 'image-optimizer'],
  '.mp4': ['video-converter']
};
```

### Dynamic Registration

Apps can register file type support:

1. **Via config.json** (recommended)
2. **Programmatically** via API
3. **Auto-inference** from app name/description

### Compatibility Scoring

Apps are ranked by:
- **Base score** (10 points) for supporting the file type
- **Name match bonus** (5 points) for exact extension matches
- **Recent usage bonus** (3 points) for apps used within 7 days
- **High usage bonus** (2 points) for apps with >5 runs

## 🎨 UI Design

### Apple Design Guidelines

- **Native macOS colors** (#007AFF, #86868b, #d1d1d6)
- **SF Pro font stack** (-apple-system)
- **8px grid system** for consistent spacing
- **Subtle borders** (0.5px) and shadows
- **Proper focus states** with blue outline

### Dialog Features

- **File preview** with size and type badges
- **App compatibility** indicators
- **Usage statistics** display
- **Responsive layout** for different screen sizes
- **Keyboard navigation** support

## 🧪 Testing

### Run Tests

```bash
# Run QuickAction-specific tests
npm run test:e2e -- tests/quickaction.test.js

# Run all tests
npm test
```

### Manual Testing

1. **Create test files** with different extensions
2. **Right-click in Finder** and select QuickAction
3. **Verify dialog appearance** and app matching
4. **Test file conversion** functionality
5. **Check output folder** opening

### Debug Logging

QuickAction logs are written to:
- **Console**: Node.js console via Signale
- **File**: `/tmp/fileduck-quickaction.log` (workflow logs)

## 🔧 Configuration

### QuickAction Settings

```javascript
const config = {
  maxFiles: 10,                    // Maximum files per action
  maxFileSize: 100 * 1024 * 1024, // 100MB limit
  autoSelectSingleApp: true,       // Auto-select if only one app
  showRecentlyUsed: true,         // Show usage statistics
  blockedExtensions: ['.exe', '.bat', '.sh', '.cmd']
};
```

### Workflow Customization

Edit `resources/FileDuck Dev Convert.workflow/Contents/Info.plist` to:
- Change supported file types
- Modify workflow name
- Update shell script behavior

## 🚨 Troubleshooting

### Common Issues

1. **QuickAction not appearing**
   - Restart Finder: `killall Finder`
   - Check workflow installation: `ls ~/Library/Services/`
   - Verify permissions: `ls -la ~/Library/Services/FileDuck*`

2. **Files not being processed**
   - Check debug logs: `tail -f /tmp/fileduck-quickaction.log`
   - Verify file paths in CLI arguments
   - Ensure FileDuck is properly installed

3. **No compatible apps found**
   - Install apps from Tool Store
   - Check app config.json for `supportedFileTypes`
   - Verify file extensions are supported

### Debug Commands

```bash
# Check workflow installation
ls -la ~/Library/Services/FileDuck*

# View workflow logs
tail -f /tmp/fileduck-quickaction.log

# Test workflow manually
cd /path/to/FileDuck-OS-Desktop
npm start -- /path/to/test.csv

# Check running processes
pgrep -f "npm.*start"
```

## 🔮 Future Enhancements

### Planned Features

1. **Production Workflow** - Signed workflow for production builds
2. **Batch Processing** - Handle multiple file types simultaneously
3. **Custom Actions** - User-defined conversion chains
4. **Cloud Integration** - Direct upload to cloud services
5. **Preview Mode** - Quick file preview before conversion

### Extension Points

- **Custom file type handlers**
- **Plugin system** for third-party integrations
- **Workflow templates** for common tasks
- **API endpoints** for external tool integration

## 📚 References

- [macOS Quick Actions Documentation](https://support.apple.com/guide/automator/create-a-quick-action-aut73234890a/mac)
- [Electron IPC Documentation](https://www.electronjs.org/docs/latest/tutorial/ipc)
- [Apple Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/macos/overview/themes/)
- [FileDuck App SDK Guidelines](./fileduck-app-sdk-guideline.md)
