# macOS-Inspired Design Guidelines for Electron Apps

## 🎯 Overview
This document provides comprehensive design guidelines to create elegant, macOS-inspired Electron applications using shadcn/ui, Tailwind CSS, and Framer Motion. The goal is to achieve the refined, eye-soothing aesthetic that makes macOS apps feel premium and approachable.

## 📚 Required Dependencies

```bash
# Core UI and Animation
npm install framer-motion lucide-react
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu
npm install @radix-ui/react-tooltip @radix-ui/react-popover

# Additional recommended packages
npm install clsx tailwind-merge
npm install @tabler/icons-react # Alternative icon set
npm install react-hotkeys-hook # For keyboard shortcuts
```

## 🎨 Color System

### Base Colors (Tailwind Config)
```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        // macOS-inspired neutrals
        macos: {
          bg: '#f5f5f7',           // Main background
          surface: '#ffffff',       // Card/surface background
          elevated: '#fbfbfb',      // Elevated surfaces
          border: '#e5e5e7',        // Subtle borders
          divider: '#d2d2d7',       // Dividers
          text: {
            primary: '#1d1d1f',     // Primary text
            secondary: '#86868b',   // Secondary text
            tertiary: '#afafaf',    // Disabled/tertiary text
          }
        },
        // System colors
        system: {
          blue: '#007aff',
          green: '#34c759',
          orange: '#ff9500',
          red: '#ff3b30',
          purple: '#af52de',
          pink: '#ff2d92',
        }
      },
      fontFamily: {
        system: ['-apple-system', 'BlinkMacSystemFont', 'San Francisco', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      fontSize: {
        'xs': ['11px', '16px'],
        'sm': ['12px', '18px'],
        'base': ['13px', '20px'],
        'lg': ['14px', '22px'],
        'xl': ['16px', '24px'],
        '2xl': ['20px', '28px'],
        '3xl': ['24px', '32px'],
      }
    }
  }
}
```

## 🎭 Typography Guidelines

### Font Hierarchy
```tsx
// Typography Component Examples
const Typography = {
  // Large titles (28px)
  title1: "text-3xl font-semibold tracking-tight text-macos-text-primary",
  
  // Section headers (20px)
  title2: "text-2xl font-medium tracking-tight text-macos-text-primary",
  
  // Subsection headers (16px)
  title3: "text-xl font-medium text-macos-text-primary",
  
  // Body text (13px)
  body: "text-base text-macos-text-primary leading-relaxed",
  
  // Secondary text (13px)
  bodySecondary: "text-base text-macos-text-secondary leading-relaxed",
  
  // Small text (12px)
  caption: "text-sm text-macos-text-secondary",
  
  // Tiny text (11px)
  footnote: "text-xs text-macos-text-tertiary",
}
```

### Usage Example
```tsx
<div className="space-y-4">
  <h1 className={Typography.title1}>Welcome to Your App</h1>
  <h2 className={Typography.title2}>Getting Started</h2>
  <p className={Typography.body}>
    This is the main body text that should be easy to read and comfortable for extended use.
  </p>
  <p className={Typography.caption}>Last updated 2 minutes ago</p>
</div>
```

## 🏗️ Layout & Spacing

### Spacing Scale
Use consistent spacing based on 4px increments:
```tsx
const spacing = {
  xs: "2px",    // 0.5
  sm: "4px",    // 1
  md: "8px",    // 2
  lg: "12px",   // 3
  xl: "16px",   // 4
  "2xl": "20px", // 5
  "3xl": "24px", // 6
  "4xl": "32px", // 8
  "5xl": "48px", // 12
}
```

### Container Guidelines
```tsx
// Main application container
<div className="min-h-screen bg-macos-bg font-system">
  {/* Sidebar */}
  <aside className="w-64 bg-macos-surface border-r border-macos-border">
    <div className="p-6">
      {/* Sidebar content with proper padding */}
    </div>
  </aside>
  
  {/* Main content */}
  <main className="flex-1 p-8">
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Content with breathing room */}
    </div>
  </main>
</div>
```

## 🎪 Component Design Patterns

### Cards and Surfaces
```tsx
// Primary card style
const Card = ({ children, elevated = false }) => (
  <div className={`
    rounded-xl 
    ${elevated ? 'bg-macos-elevated' : 'bg-macos-surface'} 
    border border-macos-border 
    shadow-sm 
    p-6
  `}>
    {children}
  </div>
)

// Usage
<Card elevated>
  <h3 className={Typography.title3}>Card Title</h3>
  <p className={Typography.body}>Card content goes here...</p>
</Card>
```

### Buttons
```tsx
// Button variants following macOS design
const Button = ({ variant = "primary", size = "md", children, ...props }) => {
  const baseClasses = "inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-system-blue/50"
  
  const variants = {
    primary: "bg-system-blue text-white hover:bg-system-blue/90 active:bg-system-blue/80",
    secondary: "bg-macos-surface border border-macos-border text-macos-text-primary hover:bg-macos-elevated active:bg-macos-border/50",
    ghost: "text-macos-text-primary hover:bg-macos-elevated active:bg-macos-border/30",
  }
  
  const sizes = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
  }
  
  return (
    <button 
      className={`${baseClasses} ${variants[variant]} ${sizes[size]}`}
      {...props}
    >
      {children}
    </button>
  )
}
```

### Input Fields
```tsx
const Input = ({ label, error, ...props }) => (
  <div className="space-y-2">
    {label && <label className={Typography.bodySecondary}>{label}</label>}
    <input
      className="
        w-full px-4 py-3 
        bg-macos-surface 
        border border-macos-border 
        rounded-lg 
        text-macos-text-primary 
        placeholder-macos-text-tertiary
        focus:outline-none 
        focus:ring-2 
        focus:ring-system-blue/50 
        focus:border-system-blue
        transition-all duration-200
      "
      {...props}
    />
    {error && <p className="text-system-red text-sm">{error}</p>}
  </div>
)
```

## 🎬 Animation Guidelines

### Framer Motion Variants
```tsx
// Standard easing curves
export const easings = {
  smooth: [0.25, 0.1, 0.25, 1],
  snappy: [0.4, 0, 0.2, 1],
  gentle: [0.16, 1, 0.3, 1],
}

// Common animation variants
export const fadeIn = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.3, ease: easings.smooth }
}

export const scaleIn = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
  transition: { duration: 0.2, ease: easings.snappy }
}

export const slideIn = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 20 },
  transition: { duration: 0.3, ease: easings.smooth }
}
```

### Animated Components
```tsx
import { motion } from 'framer-motion'

// Animated page wrapper
const PageWrapper = ({ children }) => (
  <motion.div
    initial="initial"
    animate="animate"
    exit="exit"
    variants={fadeIn}
    className="space-y-8"
  >
    {children}
  </motion.div>
)

// Staggered list animation
const listVariants = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 }
}

const AnimatedList = ({ items }) => (
  <motion.div variants={listVariants} initial="initial" animate="animate">
    {items.map((item, index) => (
      <motion.div key={index} variants={itemVariants}>
        {item}
      </motion.div>
    ))}
  </motion.div>
)
```

## 🎯 Window & Chrome Styling

### Frameless Window Setup (Electron Main Process)
```javascript
// main.js
const mainWindow = new BrowserWindow({
  width: 1200,
  height: 800,
  titleBarStyle: 'hiddenInset', // macOS style
  frame: false,
  webPreferences: {
    nodeIntegration: true,
    contextIsolation: false
  }
})
```

### Custom Title Bar Component
```tsx
const TitleBar = () => (
  <div className="
    h-12 
    bg-macos-surface/80 
    backdrop-blur-xl 
    border-b border-macos-border/50 
    flex items-center justify-between 
    px-6
    drag-region
  ">
    <div className="flex items-center space-x-4">
      <div className="flex space-x-2">
        <div className="w-3 h-3 rounded-full bg-system-red"></div>
        <div className="w-3 h-3 rounded-full bg-system-orange"></div>
        <div className="w-3 h-3 rounded-full bg-system-green"></div>
      </div>
      <h1 className={Typography.body}>Your App Name</h1>
    </div>
    
    <div className="flex items-center space-x-2 no-drag">
      {/* Window controls */}
    </div>
  </div>
)
```

## 🎨 Icon Guidelines

### Icon Usage
```tsx
import { Search, Settings, Bell, ChevronRight } from 'lucide-react'

// Icon sizing
const iconSizes = {
  xs: "w-3 h-3",   // 12px
  sm: "w-4 h-4",   // 16px
  md: "w-5 h-5",   // 20px
  lg: "w-6 h-6",   // 24px
  xl: "w-8 h-8",   // 32px
}

// Icon with text
<div className="flex items-center space-x-2">
  <Search className="w-4 h-4 text-macos-text-secondary" />
  <span className={Typography.body}>Search</span>
</div>
```

## 🎭 Interactive States

### Hover and Focus States
```css
/* Add to your global CSS */
.interactive-element {
  @apply transition-all duration-200 ease-out;
}

.interactive-element:hover {
  @apply bg-macos-elevated transform translate-y-[-1px];
}

.interactive-element:active {
  @apply bg-macos-border/30 transform translate-y-0;
}
```

### Loading States
```tsx
const LoadingSpinner = () => (
  <motion.div
    className="w-5 h-5 border-2 border-macos-border border-t-system-blue rounded-full"
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
  />
)
```

## 🎪 Advanced Patterns

### Sidebar with Blur Background
```tsx
const Sidebar = () => (
  <motion.aside 
    className="
      w-64 h-full 
      bg-macos-surface/80 
      backdrop-blur-xl 
      border-r border-macos-border/50
    "
    initial={{ x: -264 }}
    animate={{ x: 0 }}
    transition={{ duration: 0.3, ease: easings.smooth }}
  >
    <div className="p-6 space-y-6">
      {/* Sidebar content */}
    </div>
  </motion.aside>
)
```

### Context Menu
```tsx
const ContextMenu = ({ isOpen, onClose, position }) => (
  <AnimatePresence>
    {isOpen && (
      <motion.div
        className="
          fixed z-50 
          bg-macos-elevated/90 
          backdrop-blur-xl 
          border border-macos-border 
          rounded-lg 
          shadow-lg 
          py-2 
          min-w-48
        "
        style={{ left: position.x, top: position.y }}
        variants={scaleIn}
        initial="initial"
        animate="animate"
        exit="exit"
      >
        {/* Menu items */}
      </motion.div>
    )}
  </AnimatePresence>
)
```

## 📱 Responsive Considerations

### Breakpoints
```javascript
// tailwind.config.js breakpoints for desktop app
screens: {
  'sm': '768px',
  'md': '1024px',
  'lg': '1280px',
  'xl': '1536px',
}
```

## 🎯 Implementation Checklist

- [ ] Configure Tailwind with macOS color system
- [ ] Set up Framer Motion animations
- [ ] Implement custom title bar (if frameless)
- [ ] Create reusable component library
- [ ] Add proper focus management
- [ ] Implement keyboard shortcuts
- [ ] Test animations performance
- [ ] Ensure accessibility compliance
- [ ] Add proper error states
- [ ] Implement loading states

## 🚀 Performance Tips

1. **Optimize Animations**: Use `transform` and `opacity` for smooth 60fps animations
2. **Lazy Load**: Use `React.lazy()` for heavy components
3. **Memoization**: Use `React.memo()` for expensive renders
4. **Backdrop Filters**: Use sparingly as they can impact performance
5. **Image Optimization**: Use appropriate formats and sizes

## 📖 Additional Resources

- [Apple Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)
- [macOS Big Sur Design Resources](https://developer.apple.com/design/resources/)
- [Framer Motion Documentation](https://www.framer.com/motion/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

---

Following these guidelines will help you create an Electron app that feels native to macOS while maintaining modern web development practices. Remember to test frequently and iterate based on user feedback.