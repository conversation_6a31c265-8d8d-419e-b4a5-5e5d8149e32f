# 🧪 FileDuck Dev Right-Click Integration (macOS)

## 🎯 Goal
Enable **Quick Actions** in macOS Finder for development/debug mode. This allows right-click > FileDuck Dev Convert to open the Electron app via `npm start`, passing the selected file paths.

---

## ✅ Steps

### 1. Create a `.workflow` called `FileDuck Dev Convert.workflow`
- Use **Automator** to create a Quick Action.
- It should:
  - Receive files in **Finder**
  - Run a shell script like:

```bash
cd /ABSOLUTE/PATH/TO/FILEDUCK/PROJECT
npm start -- "$@"
```

Replace `/ABSOLUTE/PATH/...` with the actual path to your project.

This will launch `npm start` with file paths passed as CLI args to Electron.

---

### 2. Create Bash Script `scripts/install-dev-quickaction.sh`

```bash
#!/bin/bash

WORKFLOW_NAME="FileDuck Dev Convert.workflow"
WORKFLOW_SOURCE="./resources/$WORKFLOW_NAME"
WORKFLOW_TARGET="$HOME/Library/Services/$WORKFLOW_NAME"
PROJECT_DIR="$(pwd)"

echo "Installing Quick Action..."
mkdir -p "$HOME/Library/Services"
cp -R "$WORKFLOW_SOURCE" "$WORKFLOW_TARGET"

echo "Launching FileDuck Dev..."
cd "$PROJECT_DIR"
npm start
```

Make it executable:
```bash
chmod +x scripts/install-dev-quickaction.sh
```

---

### 3. Update Electron `main.ts` to handle CLI file input

```ts
const args = process.argv.slice(1);
if (args.length > 0) {
  console.log("File selected from Finder:", args);
  // Load this file in the UI
}
```

---

## 🧪 Dev Flow

After running:

```bash
./scripts/install-dev-quickaction.sh
```

You can:
- Right-click any file in Finder
- Use `Quick Actions > FileDuck Dev Convert`
- Your Electron dev app (`npm start`) launches with the file(s) as CLI args

---

## ⚠️ Notes
- macOS caches Quick Actions — you may need to log out or restart Finder to see changes.
- You can test the shell script by adding:
```bash
echo "$@" > /tmp/fileduck-log.txt
```
inside the `.workflow` to debug file path passing.


