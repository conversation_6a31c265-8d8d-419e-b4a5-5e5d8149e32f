<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AMApplicationBuild</key>
	<string>521</string>
	<key>AMApplicationVersion</key>
	<string>2.10</string>
	<key>AMDocumentVersion</key>
	<string>2</string>
	<key>actions</key>
	<array>
		<dict>
			<key>AMAccepts</key>
			<dict>
				<key>Container</key>
				<string>List</string>
				<key>Optional</key>
				<true/>
				<key>Types</key>
				<array>
					<string>com.apple.cocoa.string</string>
				</array>
			</dict>
			<key>AMActionVersion</key>
			<string>2.0.3</string>
			<key>AMApplication</key>
			<array>
				<string>Automator</string>
			</array>
			<key>AMParameterProperties</key>
			<dict>
				<key>COMMAND_STRING</key>
				<dict>
					<key>tokenizedValue</key>
					<array>
						<string>#!/bin/bash

# FileDuck Dev QuickAction Script
PROJECT_DIR="PLACEHOLDER_PROJECT_DIR"
cd "$PROJECT_DIR"

# Log the action for debugging
echo "FileDuck QuickAction triggered at $(date)" &gt;&gt; /tmp/fileduck-quickaction.log
echo "Files: $@" &gt;&gt; /tmp/fileduck-quickaction.log
echo "Project Dir: $PROJECT_DIR" &gt;&gt; /tmp/fileduck-quickaction.log

# Start FileDuck with the selected files as arguments
npm start -- "$@" &amp;</string>
					</array>
				</dict>
			</dict>
			<key>AMProvides</key>
			<dict>
				<key>Container</key>
				<string>List</string>
				<key>Types</key>
				<array>
					<string>com.apple.cocoa.string</string>
				</array>
			</dict>
			<key>ActionBundlePath</key>
			<string>/System/Library/Automator/Run Shell Script.action</string>
			<key>ActionName</key>
			<string>Run Shell Script</string>
			<key>ActionParameters</key>
			<dict>
				<key>COMMAND_STRING</key>
				<string>#!/bin/bash
PROJECT_DIR="PLACEHOLDER_PROJECT_DIR"
cd "$PROJECT_DIR"
echo "FileDuck QuickAction: $@" &gt;&gt; /tmp/fileduck-quickaction.log
npm start -- "$@" &amp;</string>
				<key>CheckedForUserDefaultShell</key>
				<true/>
				<key>inputMethod</key>
				<integer>1</integer>
				<key>shell</key>
				<string>/bin/bash</string>
				<key>source</key>
				<string></string>
			</dict>
			<key>BundleIdentifier</key>
			<string>com.apple.RunShellScript</string>
			<key>CFBundleVersion</key>
			<string>2.0.3</string>
			<key>CanShowSelectedItemsWhenRun</key>
			<false/>
			<key>CanShowWhenRun</key>
			<true/>
			<key>Category</key>
			<array>
				<string>AMCategoryUtilities</string>
			</array>
			<key>Class Name</key>
			<string>RunShellScriptAction</string>
			<key>InputUUID</key>
			<string>A8F8C5F1-B5A4-4F8D-9E2C-1A3B4C5D6E7F</string>
			<key>Keywords</key>
			<array>
				<string>Shell</string>
				<string>Script</string>
				<string>Command</string>
				<string>Run</string>
				<string>Unix</string>
			</array>
			<key>OutputUUID</key>
			<string>B9G9D6G2-C6B5-5G9E-AF3D-2B4C5D6E7F8G</string>
			<key>UUID</key>
			<string>C1H1E7H3-D7C6-6H1F-BG4E-3C5D6E7F8G9H</string>
			<key>UnlocalizedApplications</key>
			<array>
				<string>Automator</string>
			</array>
			<key>arguments</key>
			<dict>
				<key>0</key>
				<dict>
					<key>default value</key>
					<integer>1</integer>
					<key>name</key>
					<string>inputMethod</string>
					<key>required</key>
					<string>0</string>
					<key>type</key>
					<string>0</string>
					<key>uuid</key>
					<string>0</string>
				</dict>
				<key>1</key>
				<dict>
					<key>default value</key>
					<string></string>
					<key>name</key>
					<string>source</string>
					<key>required</key>
					<string>0</string>
					<key>type</key>
					<string>0</string>
					<key>uuid</key>
					<string>1</string>
				</dict>
				<key>2</key>
				<dict>
					<key>default value</key>
					<false/>
					<key>name</key>
					<string>CheckedForUserDefaultShell</string>
					<key>required</key>
					<string>0</string>
					<key>type</key>
					<string>0</string>
					<key>uuid</key>
					<string>2</string>
				</dict>
				<key>3</key>
				<dict>
					<key>default value</key>
					<string>/bin/sh</string>
					<key>name</key>
					<string>shell</string>
					<key>required</key>
					<string>0</string>
					<key>type</key>
					<string>0</string>
					<key>uuid</key>
					<string>3</string>
				</dict>
			</dict>
			<key>isViewVisible</key>
			<true/>
			<key>location</key>
			<string>449.000000:316.000000</string>
			<key>nibPath</key>
			<string>/System/Library/Automator/Run Shell Script.action/Contents/Resources/Base.lproj/main.nib</string>
		</dict>
	</array>
	<key>connectors</key>
	<dict/>
	<key>workflowMetaData</key>
	<dict>
		<key>workflowTypeIdentifier</key>
		<string>com.apple.Automator.servicesMenu</string>
	</dict>
</dict>
</plist>
