<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSServices</key>
	<array>
		<dict>
			<key>NSMenuItem</key>
			<dict>
				<key>default</key>
				<string>FileDuck Dev Convert</string>
			</dict>
			<key>NSMessage</key>
			<string>runWorkflowAsService</string>
			<key>NSRequiredContext</key>
			<dict>
				<key>NSApplicationIdentifier</key>
				<string>com.apple.finder</string>
			</dict>
			<key>NSSendFileTypes</key>
			<array>
				<string>public.item</string>
			</array>
		</dict>
	</array>
	<key>CFBundleIdentifier</key>
	<string>com.fileduck.quickaction.dev</string>
	<key>CFBundleName</key>
	<string>FileDuck Dev Convert</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>AMApplication</key>
	<array>
		<string>Automator</string>
	</array>
	<key>AMApplicationBuild</key>
	<string>521</string>
	<key>AMApplicationVersion</key>
	<string>2.10</string>
	<key>AMDocumentVersion</key>
	<string>2</string>
	<key>actions</key>
	<array>
		<dict>
			<key>AMAccepts</key>
			<dict>
				<key>Container</key>
				<string>List</string>
				<key>Optional</key>
				<true/>
				<key>Types</key>
				<array>
					<string>com.apple.cocoa.string</string>
				</array>
			</dict>
			<key>AMActionVersion</key>
			<string>2.0.3</string>
			<key>AMApplication</key>
			<array>
				<string>Automator</string>
			</array>
			<key>AMProvides</key>
			<dict>
				<key>Container</key>
				<string>List</string>
				<key>Types</key>
				<array>
					<string>com.apple.cocoa.string</string>
				</array>
			</dict>
			<key>ActionBundlePath</key>
			<string>/System/Library/Automator/Run Shell Script.action</string>
			<key>ActionName</key>
			<string>Run Shell Script</string>
			<key>ActionParameters</key>
			<dict>
				<key>COMMAND_STRING</key>
				<string>#!/bin/bash
PROJECT_DIR="PLACEHOLDER_PROJECT_DIR"
cd "$PROJECT_DIR"
echo "FileDuck QuickAction: $@" >> /tmp/fileduck-quickaction.log
npm start -- "$@" &amp;</string>
				<key>CheckedForUserDefaultShell</key>
				<true/>
				<key>inputMethod</key>
				<integer>1</integer>
				<key>shell</key>
				<string>/bin/bash</string>
				<key>source</key>
				<string></string>
			</dict>
			<key>BundleIdentifier</key>
			<string>com.apple.RunShellScript</string>
			<key>CFBundleVersion</key>
			<string>2.0.3</string>
			<key>CanShowSelectedItemsWhenRun</key>
			<false/>
			<key>CanShowWhenRun</key>
			<true/>
			<key>Category</key>
			<array>
				<string>AMCategoryUtilities</string>
			</array>
			<key>Class Name</key>
			<string>RunShellScriptAction</string>
			<key>InputUUID</key>
			<string>A8F8C5F1-B5A4-4F8D-9E2C-1A3B4C5D6E7F</string>
			<key>OutputUUID</key>
			<string>B9G9D6G2-C6B5-5G9E-AF3D-2B4C5D6E7F8G</string>
			<key>UUID</key>
			<string>C1H1E7H3-D7C6-6H1F-BG4E-3C5D6E7F8G9H</string>
		</dict>
	</array>
	<key>connectors</key>
	<dict/>
	<key>workflowMetaData</key>
	<dict>
		<key>workflowTypeIdentifier</key>
		<string>com.apple.Automator.servicesMenu</string>
	</dict>
</dict>
</plist>
